# -*- coding: utf-8 -*-
"""
T主连择时 | 周频回测（做空、无成本、Rf=0，允许过拟合选参）
数据文件：
  /Users/<USER>/Desktop/T主连择时-最终版.xlsx
    - sheet1: 日频 T主连行情（已过滤非交易日）
      列：日期 收盘价 结算价 开盘价 最高价 最低价 成交量 成交额 涨停价 跌停价 持仓量
    - sheet2: 周频 利率综合领先指数
      列：日期 利率综合领先指数

输出：
  /Users/<USER>/Desktop/T主连量化择时结果/
    - 每策略：净值对比&最大回撤阴影、月度收益分布直方图、月度收益时间序列柱状图、累计超额收益
    - 每策略：周/月度明细与指标（xlsx） + 最优参数表
    - 汇总：三策略与基准对比指标表（xlsx）
"""

from __future__ import annotations
import os, math, warnings, itertools, json
from pathlib import Path
from typing import Tuple, Dict, List

import numpy as np
import pandas as pd
import matplotlib
import matplotlib.pyplot as plt

warnings.filterwarnings("ignore")

# ==============================
# 全局参数 & 路径
# ==============================
DATA_FILE  = Path("/Users/<USER>/Desktop/T主连择时-最终版.xlsx")
RESULT_DIR = Path("/Users/<USER>/Desktop/T主连量化择时结果-做空")

# 频率与窗口
WEEK_FREQ      = "W-SAT"  # 与CLI对齐，周六为周末
ROLL_WIN_W     = 52       # 所有z/分位窗口
CLI_LAG_SET    = (2,3,4,5,6)  # CLI滞后候选
EMA_SPAN       = 20
MACD_FAST      = 12
MACD_SLOW      = 26
MACD_SIGNAL    = 9
MOM_LAG_W      = 4
ATR_SPAN       = 14
RVOL_WIN       = 8

# 噪声权重分位与档位（将以参数覆盖“极噪权重”）
ATR_P85, ATR_P95 = 0.85, 0.95
AMI_P70, AMI_P90 = 0.70, 0.90

# 回测区间（对齐你的要求：52W滚动后实测 2020/01/04 - 2025/07/26）
BT_START = pd.Timestamp("2020-01-04")
BT_END   = pd.Timestamp("2024-01-01")
FORCE_BEGIN = pd.Timestamp("2020-01-04")  # 与BT_START一致

# 目标函数权重（可调）
OBJ_W = dict(sharpe=0.2, cagr=0.5, win=0.2, mdd=0.1)  # score=0.4*Sharpe+0.3*CAGR+0.2*周胜率-0.1*|MDD|

# ==============================
# 字体设置（中文优先）
# ==============================
def setup_chinese_font():
    candidates = [
        "/System/Library/Fonts/PingFang.ttc",
        "/Library/Fonts/NotoSansSC-Regular.otf",
        "/System/Library/Fonts/STHeiti Medium.ttc",
        "/System/Library/Fonts/Hiragino Sans GB W3.otf",
    ]
    picked = None
    for p in candidates:
        if os.path.exists(p):
            try:
                matplotlib.font_manager.fontManager.addfont(p)
                picked = matplotlib.font_manager.FontProperties(fname=p).get_name()
                break
            except Exception:
                continue
    plt.rcParams["font.sans-serif"] = [picked or "DejaVu Sans"]
    plt.rcParams["axes.unicode_minus"] = False

setup_chinese_font()

# ==============================
# 工具函数：指标/统计
# ==============================
def ensure_dir(p: Path) -> None:
    p.mkdir(parents=True, exist_ok=True)

def rolling_mad(a: np.ndarray) -> float:
    if len(a) == 0: return np.nan
    med = np.median(a)
    return np.median(np.abs(a - med))

def robust_z(s: pd.Series, win: int) -> pd.Series:
    med = s.rolling(win, min_periods=win).median()
    mad = s.rolling(win, min_periods=win).apply(rolling_mad, raw=True)
    return (s - med) / (1.4826 * mad.replace(0, np.nan))

def max_drawdown(nav: pd.Series) -> Tuple[float, pd.Timestamp, pd.Timestamp]:
    if nav.empty: return 0.0, pd.NaT, pd.NaT
    roll_max = nav.cummax()
    dd = nav / roll_max - 1.0
    mdd = dd.min()
    end = dd.idxmin()
    start = (nav.loc[:end]).idxmax()
    return float(mdd), start, end

def cagr(weekly_ret: pd.Series) -> float:
    r = weekly_ret.dropna()
    if r.empty: return 0.0
    nav = (1 + r).cumprod()
    years = r.size / 52.0
    return float(nav.iloc[-1] ** (1 / years) - 1) if years > 0 else 0.0

def ann_vol(weekly_ret: pd.Series) -> float:
    return float(weekly_ret.std(ddof=0) * math.sqrt(52))

def sharpe(weekly_ret: pd.Series) -> float:
    if weekly_ret.std(ddof=0) == 0 or weekly_ret.dropna().empty: return 0.0
    return float(weekly_ret.mean() / weekly_ret.std(ddof=0) * math.sqrt(52))

def info_ratio(weekly_ret: pd.Series, bench_ret: pd.Series) -> float:
    diff = (weekly_ret - bench_ret).dropna()
    if diff.std(ddof=0) == 0 or diff.empty: return 0.0
    return float(diff.mean() / diff.std(ddof=0) * math.sqrt(52))

def monthly_from_weekly(weekly_ret: pd.Series) -> pd.Series:
    df = weekly_ret.dropna().to_frame("ret")
    df["m"] = df.index.to_period("M")
    out = df.groupby("m")["ret"].apply(lambda x: (1 + x).prod() - 1)
    out.index = out.index.to_timestamp("M")
    return out

def pct_rank_last(x: pd.Series) -> float:
    if x.empty: return 0.5
    r = x.rank(method="average")
    return float(r.iloc[-1] / len(r))

def objective_score(strat_ret: pd.Series) -> float:
    nav = (1 + strat_ret).cumprod()
    mdd, _, _ = max_drawdown(nav)
    win_rate = (strat_ret > 0).mean()
    return (OBJ_W["sharpe"] * sharpe(strat_ret)
           +OBJ_W["cagr"]   * cagr(strat_ret)
           +OBJ_W["win"]    * win_rate
           -OBJ_W["mdd"]    * abs(mdd))

# ==============================
# 数据加载与周频加工
# ==============================
def load_excel_pair(data_file: Path) -> tuple[pd.DataFrame, pd.DataFrame]:
    assert data_file.exists(), f"文件不存在：{data_file}"
    df_d = pd.read_excel(data_file, sheet_name=0)
    df_c = pd.read_excel(data_file, sheet_name=1)
    df_d.rename(columns=lambda c: str(c).strip(), inplace=True)
    df_c.rename(columns=lambda c: str(c).strip(), inplace=True)
    df_d["日期"] = pd.to_datetime(df_d["日期"])
    df_c["日期"] = pd.to_datetime(df_c["日期"])
    return df_d, df_c

def to_weekly(df_daily: pd.DataFrame) -> pd.DataFrame:
    df = df_daily.set_index("日期").sort_index()
    w = pd.DataFrame(index=pd.date_range(df.index.min(), df.index.max(), freq=WEEK_FREQ))
    # 价
    w["收盘价"] = df["收盘价"].resample(WEEK_FREQ).last()
    w["开盘价"] = df["开盘价"].resample(WEEK_FREQ).first()
    w["最高价"] = df["最高价"].resample(WEEK_FREQ).max()
    w["最低价"] = df["最低价"].resample(WEEK_FREQ).min()
    w["结算价"] = (df["结算价"] if "结算价" in df.columns else df["收盘价"]).resample(WEEK_FREQ).last()
    # 量/额/持仓
    w["成交量"] = df["成交量"].resample(WEEK_FREQ).sum()
    w["成交额"] = (df["成交额"] if "结算价" in df.columns else df["成交量"]*df["收盘价"]).resample(WEEK_FREQ).sum()
    w["持仓量_mean"] = df["持仓量"].resample(WEEK_FREQ).mean()
    # Amihud用（近似）
    w["_sum_abs_ret"]  = df["收盘价"].pct_change().abs().resample(WEEK_FREQ).sum()
    w["_sum_turnover"] = (df["成交量"] * df["收盘价"]).resample(WEEK_FREQ).sum()
    return w

def tech_indicators(w: pd.DataFrame) -> pd.DataFrame:
    out = w.copy()
    close = out["收盘价"]
    out["ret_w"] = close.pct_change()
    out["EMA20"] = close.ewm(span=EMA_SPAN, adjust=False).mean()

    ema12 = close.ewm(span=MACD_FAST, adjust=False).mean()
    ema26 = close.ewm(span=MACD_SLOW, adjust=False).mean()
    macd = ema12 - ema26
    out["MACD_hist"] = macd - macd.ewm(span=MACD_SIGNAL, adjust=False).mean()

    out["MOM4"]   = close / close.shift(MOM_LAG_W) - 1.0
    out["VOI"]    = out["成交量"] / out["持仓量_mean"].replace(0, np.nan)
    out["RVOL8"]  = out["成交量"] / (out["成交量"].shift(1).rolling(RVOL_WIN, min_periods=RVOL_WIN).mean())

    prev_close = close.shift(1)
    tr = pd.concat([(out["最高价"] - out["最低价"]),
                    (out["最高价"] - prev_close).abs(),
                    (out["最低价"] - prev_close).abs()], axis=1).max(axis=1)
    out["ATR14"]     = tr.ewm(span=ATR_SPAN, adjust=False).mean()
    out["ATR_ratio"] = out["ATR14"] / close

    out["Amihud_w"]  = out["_sum_abs_ret"] / out["_sum_turnover"].replace(0, np.nan)
    return out

def noise_weight(df: pd.DataFrame, extreme_weight: float = 0.25, win: int = ROLL_WIN_W) -> pd.Series:
    atr, ami = df["ATR_ratio"], df["Amihud_w"]
    roll_q = lambda s, q: s.rolling(win, min_periods=win).quantile(q)
    atr85, atr95 = roll_q(atr, ATR_P85), roll_q(atr, ATR_P95)
    ami70, ami90 = roll_q(ami, AMI_P70), roll_q(ami, AMI_P90)

    w = pd.Series(1.0, index=df.index)
    w[(atr >= atr95) | (ami >= ami90)] = extreme_weight
    mask_half = (atr >= atr85) | (ami >= ami70)
    w[(w == 1.0) & mask_half] = 0.5
    return w

# ==============================
# CLI 分数（可过拟合选参）
# ==============================
def compute_cli_raw(cli_week: pd.DataFrame, weekly_index: pd.DatetimeIndex, alpha: float) -> pd.Series:
    s = cli_week.set_index("日期").sort_index().reindex(weekly_index)["利率综合领先指数"]
    z_diff = robust_z(s.diff(), ROLL_WIN_W)
    z_lvl  = robust_z(s,       ROLL_WIN_W)
    s_raw  = alpha * z_diff + (1 - alpha) * z_lvl
    return s_raw

def choose_lag_and_orient(s_raw: pd.Series, ret_w: pd.Series, lag_set: Tuple[int,...]) -> Tuple[int, int]:
    # 在全样本上按 Spearman-IC 选择 k* 与方向（允许过拟合）
    ic_vals = {}
    for k in lag_set:
        ic = s_raw.shift(k).corr(ret_w, method="spearman")  # S(t-k) 对应 t 的收益
        ic_vals[k] = ic if not np.isnan(ic) else 0.0
    k_star = max(ic_vals, key=lambda k: abs(ic_vals[k]))
    orient = 1 if ic_vals[k_star] >= 0 else -1
    return k_star, orient

def cli_signal_series(cli_week: pd.DataFrame, weekly_index: pd.DatetimeIndex,
                      ret_w: pd.Series, alpha: float, lag_set=CLI_LAG_SET) -> Tuple[pd.Series, Dict[str,float]]:
    # 组合z并选择 k* 与方向，得到 S_cli（已经济含义纠偏）
    s_raw = compute_cli_raw(cli_week, weekly_index, alpha)
    k_star, orient = choose_lag_and_orient(s_raw, ret_w, lag_set)
    S_cli = - orient * s_raw.shift(k_star)
    # 全样本分位（过拟合版）：q10,q15,q20,q30,q40,q80
    qs = S_cli.quantile([0.10,0.15,0.20,0.30,0.40,0.80]).to_dict()
    return S_cli, {"k_star":k_star, "orient":orient, **{f"q{int(k*100)}":v for k,v in zip([0.10,0.15,0.20,0.30,0.40,0.80], qs.values())}}

# ==============================
# 技术分数（松耦合、可过拟合）
# ==============================
def tech_scores(df: pd.DataFrame, tau_mom: float, tau_rvol: float, w123: Tuple[float,float,float]) -> Tuple[pd.Series, Dict[str, pd.Series]]:
    close, ema20 = df["收盘价"], df["EMA20"]
    # 趋势分数：d_ema做分位映射+EMA上行奖励
    d_ema  = close / ema20 - 1.0
    rank_dem = d_ema.rolling(ROLL_WIN_W, min_periods=ROLL_WIN_W).apply(pct_rank_last, raw=False)
    slope  = (ema20/ema20.shift(1) - 1.0).fillna(0)
    score_trend = (rank_dem + 0.2*(slope>0).astype(float)).clip(0,1)

    # 动量分数：MOM4 vs tau + MACD_hist 改善
    improve = (df["MACD_hist"] >= df["MACD_hist"].ewm(span=4, adjust=False).mean()).astype(float)
    score_mom = 0.5*((df["MOM4"] >= tau_mom).astype(float)) + 0.5*improve

    # 量能分数：VOI分位 + RVOL阈
    voi_rank = df["VOI"].rolling(ROLL_WIN_W, min_periods=ROLL_WIN_W).apply(pct_rank_last, raw=False)
    rvol_flag = (df["RVOL8"] >= tau_rvol).astype(float)
    score_vol = 0.5*voi_rank + 0.5*rvol_flag

    w1,w2,w3 = w123
    score_tech = (w1*score_trend + w2*score_mom + w3*score_vol).clip(0,1)

    return score_tech, dict(trend=score_trend, mom=score_mom, vol=score_vol, voi_rank=voi_rank, rvol_flag=rvol_flag)

# ==============================
# 仓位生成（状态机，允许多/空，t产生→t+1生效）
# ==============================
def build_positions_strategy1(S_cli: pd.Series, q_in: float, q_out: float,
                              W_noise: pd.Series, extreme_reversal: float = 0.0) -> pd.Series:
    # 允许卖空版本：S<=q_in 做多，S>=q80 做空；多仓出场 S>=q_out；空仓出场 S<=q40
    q80 = S_cli.quantile(0.80)
    q40 = S_cli.quantile(0.40)

    pos = pd.Series(0.0, index=S_cli.index)
    holding_long = False
    holding_short = False

    for t in range(len(S_cli)):
        s = S_cli.iloc[t]

        # 出场优先
        if holding_long and (s >= q_out):
            holding_long = False
        if holding_short and (s <= q40):
            holding_short = False

        # 进场（互斥）
        if (not holding_long) and (not holding_short):
            if s <= q_in:
                holding_long = True
            elif s >= q80:
                holding_short = True

        # 极端反打（保留原语义，对称处理）
        if (not holding_long) and (not holding_short) and (extreme_reversal > 0):
            if s >= q80:
                pos.iloc[t] = -extreme_reversal
            elif s <= q_in:
                pos.iloc[t] =  extreme_reversal
            else:
                pos.iloc[t] = 0.0
        else:
            pos.iloc[t] = 1.0 if holding_long else (-1.0 if holding_short else 0.0)

    pos = (pos * W_noise).clip(-1, 1)
    return pos.shift(1)

def build_positions_strategy2(score_tech: pd.Series, score_vol: pd.Series,
                              theta_in: float, theta_out: float,
                              W_noise: pd.Series) -> pd.Series:
    pos = pd.Series(0.0, index=score_tech.index)
    holding_long = False
    holding_short = False

    lo_in  = 1 - theta_in
    lo_out = 1 - theta_out

    for t in range(len(score_tech)):
        s, v = score_tech.iloc[t], score_vol.iloc[t]

        # 出场
        if holding_long  and (s <= theta_out): holding_long  = False
        if holding_short and (s >= lo_out):    holding_short = False

        # 进场（互斥，需量能确认）
        if (not holding_long) and (not holding_short) and (v >= 0.5):
            if s >= theta_in:
                holding_long = True
            elif s <= lo_in:
                holding_short = True

        pos.iloc[t] = 1.0 if holding_long else (-1.0 if holding_short else 0.0)

    pos = (pos * W_noise).clip(-1, 1)
    return pos.shift(1)

def build_positions_strategy3(S_cli: pd.Series, score_tech: pd.Series, score_vol: pd.Series,
                              q_in: float, beta_vote: float, pos_base: float,
                              W_noise: pd.Series) -> pd.Series:
    # 软投票：≥0.6 看多，≤0.4 看空；量能≥0.5 作确认；强信号放大后统一 clip(-1,1)
    cli_long  = (S_cli <= q_in).astype(int)
    tech_long = (score_tech >= score_tech.quantile(0.60)).astype(int)

    pos = pd.Series(0.0, index=S_cli.index)
    for t in range(len(S_cli)):
        v  = beta_vote*cli_long.iloc[t] + (1-beta_vote)*tech_long.iloc[t]  # ∈[0,1]
        sv = score_vol.iloc[t]

        if (v >= 0.6) or (0.4 <= v < 0.6 and sv >= 0.5):
            strong_long = (cli_long.iloc[t]==1) and (tech_long.iloc[t]==1) and (abs(S_cli.iloc[t]) >= 0.8)
            base = 1.25 if strong_long else pos_base
            pos.iloc[t] =  base
        elif (v <= 0.4) or (0.4 < v < 0.6 and sv >= 0.5 and cli_long.iloc[t]==0 and tech_long.iloc[t]==0):
            strong_short = (cli_long.iloc[t]==0) and (tech_long.iloc[t]==0) and (abs(S_cli.iloc[t]) >= 0.8)
            base = 1.25 if strong_short else pos_base
            pos.iloc[t] = -base
        else:
            pos.iloc[t] = 0.0

    pos = (pos * W_noise).clip(-1, 1)
    return pos.shift(1)

# ==============================
# 回测 & 评估 & 作图
# ==============================
def backtest(weekly_close: pd.Series, pos: pd.Series) -> tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    ret_w = weekly_close.pct_change().fillna(0)
    strat_ret = (pos.fillna(0) * ret_w).fillna(0)
    bench_ret = ret_w
    return strat_ret, (1 + strat_ret).cumprod(), bench_ret, (1 + bench_ret).cumprod()

def perf_table(strat_ret: pd.Series, bench_ret: pd.Series) -> pd.Series:
    active = strat_ret != 0
    mdd, dd_s, dd_e = max_drawdown((1 + strat_ret).cumprod())
    return pd.Series({
        "样本周数": strat_ret.dropna().size,
        "CAGR": cagr(strat_ret),
        "年化波动": ann_vol(strat_ret),
        "夏普": sharpe(strat_ret),
        "最大回撤": mdd,
        "最长回撤开始": dd_s,
        "最长回撤结束": dd_e,
        "卡玛比率": (cagr(strat_ret) / abs(mdd)) if mdd < 0 else np.nan,
        "周胜率": (strat_ret > 0).mean(),
        "主动周胜率(持仓周)": (strat_ret[active] > 0).mean() if active.any() else np.nan,
        "信息比率(相对基准)": info_ratio(strat_ret, bench_ret),
    })

def plot_nav_dd(title: str, strat_nav: pd.Series, bench_nav: pd.Series, outdir: Path):
    fig, ax = plt.subplots(figsize=(11, 6))
    ax.plot(strat_nav.index, strat_nav, label=title, linewidth=1.6)
    ax.plot(bench_nav.index, bench_nav, label="基准（买入并持有）", linewidth=1.2)
    # 倒置回撤阴影（相对策略净值的峰值）
    roll_max = strat_nav.cummax()
    dd = strat_nav / roll_max - 1.0  # <=0
    ax.fill_between(dd.index, 1 + dd.values, 1, step="pre", alpha=0.25)
    ax.set_title(f"{title} vs 基准：累计净值与最大回撤阴影")
    ax.set_ylabel("净值"); ax.grid(True, linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(outdir / "累计净值_含回撤阴影.png", dpi=200, bbox_inches='tight')
    plt.close(fig)

def plot_monthly_hist(title: str, m_strat: pd.Series, m_bench: pd.Series, outdir: Path):
    data = pd.concat([m_strat, m_bench]).dropna()
    if data.empty: return
    bins = np.histogram_bin_edges(data.values, bins="auto")
    fig, ax = plt.subplots(figsize=(10, 5))
    ax.hist(m_strat.dropna().values, bins=bins, alpha=0.6, label=title)
    ax.hist(m_bench.dropna().values, bins=bins, alpha=0.6, label="基准（买入并持有）")
    ax.set_title(f"{title} vs 基准：月度收益率分布")
    ax.set_xlabel("月度收益率"); ax.set_ylabel("频数"); ax.grid(True, linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(outdir / "月度收益率分布_直方图.png", dpi=200, bbox_inches='tight')
    plt.close(fig)

def plot_excess(title: str, strat_ret: pd.Series, bench_ret: pd.Series, outdir: Path):
    strat_nav = (1 + strat_ret).cumprod()
    bench_nav = (1 + bench_ret).cumprod()
    excess = strat_nav / bench_nav - 1.0
    fig, ax = plt.subplots(figsize=(11, 4.5))
    ax.plot(excess.index, excess, linewidth=1.5)
    ax.set_title(f"{title}：累计超额收益（2019-至今）"); ax.set_ylabel("累计超额"); ax.grid(True, linestyle="--", alpha=0.3)
    plt.tight_layout()
    fig.savefig(outdir / "累计超额收益.png", dpi=200, bbox_inches='tight')
    plt.close(fig)

def plot_monthly_ts(title: str, m_strat: pd.Series, m_bench: pd.Series, outdir: Path):
    idx = m_strat.index.union(m_bench.index).sort_values()
    s, b = m_strat.reindex(idx), m_bench.reindex(idx)
    x = np.arange(len(idx)); width = 0.42
    fig, ax = plt.subplots(figsize=(12, 5))
    ax.bar(x - width/2, s.values, width=width, label=title)
    ax.bar(x + width/2, b.values, width=width, label="基准（买入并持有）")
    ax.set_title(f"{title} vs 基准：月度收益率（时间序列）")
    ax.set_xticks(x[::max(1, len(x)//12)])
    ax.set_xticklabels([d.strftime("%Y-%m") for d in idx][::max(1, len(x)//12)], rotation=45)
    ax.grid(True, axis="y", linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(outdir / "月度收益率_时间序列柱状图.png", dpi=200, bbox_inches='tight')
    plt.close(fig)

# ==============================
# 选参（过拟合模式，in-sample 全样本寻优）
# ==============================
def grid_search_strategy1(S_cli: pd.Series, W_noise: pd.Series, weekly_close: pd.Series,
                          grid: Dict, bench_ret: pd.Series) -> Tuple[pd.Series, Dict]:
    best_score, best_pos, best_params = -1e9, None, None
    q10, q15, q20, q30, q40, q80 = S_cli.quantile([0.10,0.15,0.20,0.30,0.40,0.80])
    q_map = {'q10':q10,'q15':q15,'q20':q20,'q30':q30,'q40':q40,'q80':q80}
    for q_in_name in grid["q_in_names"]:
        q_in  = q_map[q_in_name]
        for q_out_name in grid["q_out_names"]:
            q_out = q_map[q_out_name]
            if q_out < q_in: continue
            for extreme_weight in grid["extreme_weight"]:
                for gamma in grid["gamma"]:
                    pos = build_positions_strategy1(S_cli, q_in, q_out, W_noise=W_noise.replace(0,extreme_weight), extreme_reversal=gamma)
                    strat_ret, _, _, _ = backtest(weekly_close, pos)
                    score = objective_score(strat_ret)
                    if score > best_score:
                        best_score, best_pos, best_params = score, pos, dict(q_in=q_in_name, q_out=q_out_name, extreme_weight=extreme_weight, gamma=gamma, q_values=q_map)
    return best_pos, best_params

def grid_search_strategy2(scores: Dict[str,pd.Series], W_noise: pd.Series, weekly_close: pd.Series,
                          grid: Dict, bench_ret: pd.Series) -> Tuple[pd.Series, Dict]:
    best_score, best_pos, best_params = -1e9, None, None
    score_tech, score_vol = scores["tech"], scores["vol"]
    for theta_in in grid["theta_in"]:
        for theta_out in grid["theta_out"]:
            if theta_out > (theta_in - 0.05): continue
            for extreme_weight in grid["extreme_weight"]:
                pos = build_positions_strategy2(score_tech, score_vol, theta_in, theta_out, W_noise.replace(0,extreme_weight))
                strat_ret, _, _, _ = backtest(weekly_close, pos)
                score = objective_score(strat_ret)
                if score > best_score:
                    best_score, best_pos, best_params = score, pos, dict(theta_in=theta_in, theta_out=theta_out, extreme_weight=extreme_weight)
    return best_pos, best_params

def grid_search_strategy3(S_cli: pd.Series, scores: Dict[str,pd.Series], W_noise: pd.Series, weekly_close: pd.Series,
                          grid: Dict, bench_ret: pd.Series) -> Tuple[pd.Series, Dict]:
    best_score, best_pos, best_params = -1e9, None, None
    q10, q15, q20 = S_cli.quantile([0.10,0.15,0.20])
    q_map = {'q10':q10,'q15':q15,'q20':q20}
    score_tech, score_vol = scores["tech"], scores["vol"]
    for q_in_name in grid["q_in_names"]:
        q_in = q_map[q_in_name]
        for beta in grid["beta"]:
            for pos_base in grid["pos_base"]:
                for extreme_weight in grid["extreme_weight"]:
                    pos = build_positions_strategy3(S_cli, score_tech, score_vol, q_in, beta, pos_base, W_noise.replace(0,extreme_weight))
                    strat_ret, _, _, _ = backtest(weekly_close, pos)
                    score = objective_score(strat_ret)
                    if score > best_score:
                        best_score, best_pos, best_params = score, pos, dict(q_in=q_in_name, beta=beta, pos_base=pos_base, extreme_weight=extreme_weight, q_values=q_map)
    return best_pos, best_params

# ==============================
# 主流程
# ==============================
def main():
    print("="*60)
    print("T主连择时回测系统（允许过拟合选参）启动")
    print("="*60)

    ensure_dir(RESULT_DIR)

    # 1) 读取
    print("1. 正在加载数据...")
    try:
        df_daily, df_cli = load_excel_pair(DATA_FILE)
        print(f"   日频数据: {df_daily.shape}")
        print(f"   CLI数据: {df_cli.shape}")
    except Exception as e:
        print(f"   数据加载失败: {e}")
        return

    # 2) 周频聚合 & 区间裁剪
    print("2. 转换为周频数据...")
    try:
        w = to_weekly(df_daily)
        w = w.loc[(w.index >= BT_START) & (w.index <= BT_END)].copy()
        print(f"   周频数据: {w.shape}")
    except Exception as e:
        print(f"   周频转换失败: {e}")
        return

    # 3) 技术指标 & 噪声权重（先用默认极噪=0.25，选参时可覆盖）
    print("3. 计算技术指标...")
    try:
        w = tech_indicators(w)
        weekly_close = w["收盘价"]
        ret_w = weekly_close.pct_change().fillna(0)
        print(f"   技术指标计算完成")
    except Exception as e:
        print(f"   技术指标计算失败: {e}")
        return

    # 4) CLI 分数（alpha网格 + 自适应k*与方向；返回最优alpha下的S_cli）
    #    这里先挑 alpha ∈ {0.4, 0.5, 0.6} 中最优（按目标函数），固定 lag集合与方向求法
    best_alpha, best_score_alpha, best_S_cli, best_cli_meta = None, -1e9, None, None
    for alpha in (0.4, 0.5, 0.6):
        S_cli_tmp, meta_tmp = cli_signal_series(df_cli[["日期","利率综合领先指数"]], w.index, ret_w, alpha, lag_set=CLI_LAG_SET)
        # 先用一个默认仓位（q20入 q40出）+ 极噪0.25 粗算评分，挑 alpha
        q20, q40 = S_cli_tmp.quantile(0.20), S_cli_tmp.quantile(0.40)
        Wn_tmp = noise_weight(w, extreme_weight=0.25, win=ROLL_WIN_W)
        pos_tmp = build_positions_strategy1(S_cli_tmp, q20, q40, Wn_tmp, extreme_reversal=0.0)
        score_tmp = objective_score(backtest(weekly_close, pos_tmp)[0])
        if score_tmp > best_score_alpha:
            best_score_alpha, best_alpha, best_S_cli, best_cli_meta = score_tmp, alpha, S_cli_tmp, meta_tmp

    S_cli = best_S_cli
    cli_meta = best_cli_meta  # 含 k_star, orient, q10..q80（会在最终参数里覆盖具体q_in/out）

    # 5) 技术分数（参数网格）
    best_scores_pack = {}
    scores_dict_by_param = {}  # 缓存 tech 分数避免重复算
    tech_param_grid = list(itertools.product(
        (0.000, 0.001, 0.002),         # tau_mom
        (0.95, 1.00, 1.05),           # tau_rvol
        ((0.4,0.3,0.3),(0.33,0.33,0.34),(0.5,0.2,0.3)), # weights
    ))
    for tau_mom, tau_rvol, w123 in tech_param_grid:
        key = (tau_mom, tau_rvol, w123)
        score_tech, parts = tech_scores(w, tau_mom=tau_mom, tau_rvol=tau_rvol, w123=w123)
        scores_dict_by_param[key] = dict(tech=score_tech, vol=parts["vol"], voi_rank=parts["voi_rank"], rvol_flag=parts["rvol_flag"])

    # 6) 统一噪声权重（选参覆盖极噪档）
    #    先计算基础分位（不会变），后续根据极噪权重替换 W_noise 中的 0
    W_noise_base = noise_weight(w, extreme_weight=0.25, win=ROLL_WIN_W)

    # 7) 三策略过拟合选参（in-sample，全样本）
    print("\n开始三策略 in-sample 选参 ...")

    # 7.1 策略1（CLI）
    grid1 = dict(
        q_in_names = ["q10","q15","q20"],
        q_out_names= ["q30","q40"],
        extreme_weight = [0.25, 0.33, 0.50],
        gamma = [0.0, 0.25]  # 极端反打小仓
    )
    pos1, params1 = grid_search_strategy1(S_cli, W_noise_base, weekly_close, grid1, ret_w)

    # 7.2 策略2（技术）
    best_pos2, best_params2, best_score2 = None, None, -1e9
    for key, scs in scores_dict_by_param.items():
        score_tech = scs["tech"]; score_vol = scs["vol"]
        grid2 = dict(
            theta_in=[0.55,0.60,0.65],
            theta_out=[0.45,0.50,0.55],
            extreme_weight=[0.25,0.33,0.50]
        )
        pos2_tmp, params2_tmp = grid_search_strategy2(dict(tech=score_tech, vol=score_vol), W_noise_base, weekly_close, grid2, ret_w)
        score2_tmp = objective_score(backtest(weekly_close, pos2_tmp)[0])
        if score2_tmp > best_score2:
            best_score2, best_pos2, best_params2 = score2_tmp, pos2_tmp, dict(**params2_tmp, tau_mom=key[0], tau_rvol=key[1], weights=key[2])
    pos2, params2 = best_pos2, best_params2

    # 7.3 策略3（集成：软投票）
    best_pos3, best_params3, best_score3 = None, None, -1e9
    for key, scs in scores_dict_by_param.items():
        score_tech = scs["tech"]; score_vol = scs["vol"]
        grid3 = dict(
            q_in_names=["q10","q15","q20"],
            beta=[0.4,0.5,0.6,0.7],
            pos_base=[0.75,1.00],
            extreme_weight=[0.25,0.33,0.50]
        )
        pos3_tmp, params3_tmp = grid_search_strategy3(S_cli, dict(tech=score_tech, vol=score_vol), W_noise_base, weekly_close, grid3, ret_w)
        score3_tmp = objective_score(backtest(weekly_close, pos3_tmp)[0])
        if score3_tmp > best_score3:
            best_score3, best_pos3, best_params3 = score3_tmp, pos3_tmp, dict(**params3_tmp, tau_mom=key[0], tau_rvol=key[1], weights=key[2])
    pos3, params3 = best_pos3, best_params3

    # 8) 对齐（去除首周无pos）
    pos1 = pos1.reindex(weekly_close.index).fillna(0)
    pos2 = pos2.reindex(weekly_close.index).fillna(0)
    pos3 = pos3.reindex(weekly_close.index).fillna(0)

    # 9) 回测
    s1_ret, s1_nav_all, b_ret, b_nav_all = backtest(weekly_close, pos1)
    s2_ret, s2_nav_all, _, _             = backtest(weekly_close, pos2)
    s3_ret, s3_nav_all, _, _             = backtest(weekly_close, pos3)

    # 10) 限定有效起点（52W热身+信号非空+不晚于2020），并统一净值起点=1
    need_cols = ["收盘价","EMA20","MACD_hist","MOM4","VOI","RVOL8","ATR_ratio","Amihud_w"]
    valid = w[need_cols].notna().all(axis=1) & S_cli.notna()
    start = max(valid[valid].index.min(), FORCE_BEGIN)
    end = w.index.max()
    mask = (s1_ret.index >= start) & (s1_ret.index <= end)

    # 切片后重新计算净值（保证所有净值序列首值=1）
    s1_ret = s1_ret[mask]
    s2_ret = s2_ret[mask]
    s3_ret = s3_ret[mask]
    b_ret  = b_ret[mask]

    s1_nav = (1 + s1_ret).cumprod()
    s2_nav = (1 + s2_ret).cumprod()
    s3_nav = (1 + s3_ret).cumprod()
    b_nav  = (1 + b_ret ).cumprod()

    # 11) 月度收益
    m_bench = monthly_from_weekly(b_ret)
    m1, m2, m3 = map(monthly_from_weekly, (s1_ret, s2_ret, s3_ret))

    # 12) 指标
    perf_b = perf_table(b_ret, b_ret)
    perf1  = perf_table(s1_ret, b_ret)
    perf2  = perf_table(s2_ret, b_ret)
    perf3  = perf_table(s3_ret, b_ret)

    # 13) 输出目录
    dir1 = RESULT_DIR / "策略1_CLI"
    dir2 = RESULT_DIR / "策略2_技术"
    dir3 = RESULT_DIR / "策略3_集成"
    for d in (dir1, dir2, dir3): ensure_dir(d)

    # 14) 作图
    plot_nav_dd("策略1：纯CLI", s1_nav, b_nav, dir1)
    plot_nav_dd("策略2：技术因子", s2_nav, b_nav, dir2)
    plot_nav_dd("策略3：CLI+技术", s3_nav, b_nav, dir3)

    plot_monthly_hist("策略1：纯CLI", m1, m_bench, dir1)
    plot_monthly_hist("策略2：技术因子", m2, m_bench, dir2)
    plot_monthly_hist("策略3：CLI+技术", m3, m_bench, dir3)

    plot_excess("策略1：纯CLI", s1_ret, b_ret, dir1)
    plot_excess("策略2：技术因子", s2_ret, b_ret, dir2)
    plot_excess("策略3：CLI+技术", s3_ret, b_ret, dir3)

    plot_monthly_ts("策略1：纯CLI", m1, m_bench, dir1)
    plot_monthly_ts("策略2：技术因子", m2, m_bench, dir2)
    plot_monthly_ts("策略3：CLI+技术", m3, m_bench, dir3)

    # >>> 新增一条：策略2 vs 策略3 对比图（单独输出在结果根目录）
    fig, ax = plt.subplots(figsize=(11, 6))
    ax.plot(s2_nav.index, s2_nav, label="策略2：技术因子", linewidth=1.6)
    ax.plot(s3_nav.index, s3_nav, label="策略3：CLI+技术", linewidth=1.6)
    ax.set_title("策略2 vs 策略3：累计净值对比")
    ax.set_ylabel("净值"); ax.grid(True, linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(RESULT_DIR / "策略2_vs_策略3_累计净值对比.png", dpi=200, bbox_inches='tight')
    plt.close(fig)
    # <<< 新增结束

    # 15) 导出 Excel（每策略）
    def export_strategy(path: Path, name: str, strat_ret: pd.Series, strat_nav: pd.Series,
                        m_strat: pd.Series, perf: pd.Series, params: Dict):
        xlsx = path / "周度序列_与指标_与参数.xlsx"
        engine = "xlsxwriter"
        try:
            import xlsxwriter  # noqa
        except Exception:
            engine = "openpyxl"
        with pd.ExcelWriter(xlsx, engine=engine) as writer:
            pd.DataFrame({
                "周收益(策略)": strat_ret,
                "周收益(基准)": b_ret.reindex(strat_ret.index),
                "策略净值": strat_nav,
                "基准净值": b_nav.reindex(strat_nav.index),
            }).to_excel(writer, sheet_name="weekly", index_label="周末")
            pd.DataFrame({
                "月度收益(策略)": m_strat,
                "月度收益(基准)": m_bench.reindex(m_strat.index)
            }).to_excel(writer, sheet_name="monthly", index_label="月份")
            perf.to_frame(name).to_excel(writer, sheet_name="metrics")
            pd.DataFrame({"参数": [json.dumps(params, ensure_ascii=False, indent=2)]}).to_excel(writer, sheet_name="params", index=False)

    export_strategy(dir1, "策略1：纯CLI", s1_ret, s1_nav, m1, perf1, dict(alpha=best_alpha, **params1, **{k:v for k,v in cli_meta.items() if k in ("k_star","orient")} ))
    export_strategy(dir2, "策略2：技术因子", s2_ret, s2_nav, m2, perf2, params2)
    export_strategy(dir3, "策略3：CLI+技术", s3_ret, s3_nav, m3, perf3, params3)

    # 16) 汇总对比
    summary = pd.concat([
        perf_b.rename("基准（买入并持有）"),
        perf1.rename("策略1：纯CLI"),
        perf2.rename("策略2：技术因子"),
        perf3.rename("策略3：CLI+技术"),
    ], axis=1)
    summary.to_excel(RESULT_DIR / "总结_对比指标表.xlsx")

    # 控制台摘要
    print("\n" + "="*60)
    print("回测结果汇总（过拟合选参）:")
    print("="*60)
    print(f"{'指标':<20} {'基准':<12} {'策略1':<12} {'策略2':<12} {'策略3':<12}")
    print("-" * 80)
    for idx in ['CAGR', '年化波动', '夏普', '最大回撤', '周胜率']:
        if idx in summary.index:
            row = summary.loc[idx]
            print(f"{idx:<20} {row.iloc[0]:<12.4f} {row.iloc[1]:<12.4f} {row.iloc[2]:<12.4f} {row.iloc[3]:<12.4f}")

    print(f"\n完成！输出目录：{RESULT_DIR}")
    print("="*60)

# 入口
if __name__ == "__main__":
    main()
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager
import seaborn as sns
import os
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
import warnings
warnings.filterwarnings('ignore')

# ==============================================================================
# 0. 全局配置 - 超级优化版本
# ==============================================================================
file_path = "/Users/<USER>/Desktop/T主连择时-最终版.xlsx" 
output_folder = os.path.expanduser("~/Desktop/T_Futures_Timing_Results_v13_SuperOptimized") 
BACKTEST_START_DATE = '2019-01-05'
BACKTEST_END_DATE = '2025-8-01'
RISK_FREE_RATE = 0.00

# ==============================================================================
# 1. 字体设置
# ==============================================================================
def setup_english_font():
    print("正在设置英文字体...")
    try:
        plt.rcParams["font.sans-serif"] = ["DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False
        print("字体设置完成。")
    except Exception as e:
        print(f"字体设置时发生错误: {e}")

# ==============================================================================
# 2. 数据加载与预处理
# ==============================================================================
def load_and_prepare_data(excel_path, backtest_start, backtest_end):
    print("\n正在加载和预处理数据...")
    if not os.path.exists(excel_path):
        print(f"错误: 无法在以下路径找到文件: {excel_path}")
        return None
    df_daily = pd.read_excel(excel_path, sheet_name='Sheet1')
    df_weekly = pd.read_excel(excel_path, sheet_name='Sheet2')

    df_daily.rename(columns={'日期': 'date', '收盘价': 'close', '开盘价': 'open', '最高价': 'high', '最低价': 'low', '成交量': 'volume'}, inplace=True)
    df_daily['date'] = pd.to_datetime(df_daily['date'])
    df_daily.set_index('date', inplace=True)

    df_weekly.rename(columns={'日期': 'date', '利率综合领先指数': 'leading_index'}, inplace=True)
    df_weekly['date'] = pd.to_datetime(df_weekly['date'])
    df_weekly.set_index('date', inplace=True)
    df_weekly.sort_index(inplace=True)

    df_merged = pd.merge_asof(df_daily.sort_index(), df_weekly, left_index=True, right_index=True, direction='backward')
    df_merged['leading_index'] = df_merged['leading_index'].ffill()
    df_merged['daily_return'] = df_merged['close'].pct_change()
    
    df_backtest = df_merged.loc[backtest_start:backtest_end].copy()
    df_backtest['benchmark_net_value'] = (1 + df_backtest['daily_return'].fillna(0)).cumprod()
    
    print("数据加载与预处理完成。")
    return df_backtest

# ==============================================================================
# 3. 超级优化策略实现 - 完全禁止卖空
# ==============================================================================

def run_strategy_1_super_leading_index(df):
    """策略1 (超级利率领先指数): 极度优化，确保胜率>2%，禁止卖空"""
    print("运行策略1 (超级利率领先指数)...")
    df_strat = df.copy()
    
    # 极度激进参数，确保超越基准
    window = 25  # 更短窗口期
    rolling_mean = df_strat['leading_index'].rolling(window=window, min_periods=8).mean()
    rolling_std = df_strat['leading_index'].rolling(window=window, min_periods=8).std()
    df_strat['z_score'] = (df_strat['leading_index'] - rolling_mean) / rolling_std

    # 极短均线系统
    z_ma_fast_period = 1; z_ma_slow_period = 2
    df_strat['z_ma_fast'] = df_strat['z_score'].rolling(window=z_ma_fast_period).mean()
    df_strat['z_ma_slow'] = df_strat['z_score'].rolling(window=z_ma_slow_period).mean()

    # 多重领先指数衍生指标
    df_strat['leading_momentum_1d'] = df_strat['leading_index'].pct_change(1)
    df_strat['leading_momentum_2d'] = df_strat['leading_index'].pct_change(2)
    df_strat['leading_momentum_3d'] = df_strat['leading_index'].pct_change(3)
    df_strat['leading_acceleration'] = df_strat['leading_momentum_1d'].diff()
    df_strat['leading_trend'] = df_strat['leading_index'].rolling(3).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])

    # 价格与领先指数的相关性
    df_strat['price_leading_corr'] = df_strat['close'].rolling(15).corr(df_strat['leading_index'])

    # 市场状态判断
    df_strat['market_volatility'] = df_strat['daily_return'].rolling(10).std()

    df_strat['position'] = 0; position = 0
    level_threshold = 0.02  # 极低阈值
    
    for i in range(20, len(df_strat)):
        current_z = df_strat['z_score'].iloc[i]
        fast_ma = df_strat['z_ma_fast'].iloc[i]
        slow_ma = df_strat['z_ma_slow'].iloc[i]
        momentum_1d = df_strat['leading_momentum_1d'].iloc[i]
        momentum_2d = df_strat['leading_momentum_2d'].iloc[i]
        momentum_3d = df_strat['leading_momentum_3d'].iloc[i]
        acceleration = df_strat['leading_acceleration'].iloc[i]
        trend = df_strat['leading_trend'].iloc[i]
        corr = df_strat['price_leading_corr'].iloc[i]
        volatility = df_strat['market_volatility'].iloc[i]

        # 金叉判断
        is_golden_cross = fast_ma > slow_ma and df_strat['z_ma_fast'].iloc[i-1] <= df_strat['z_ma_slow'].iloc[i-1]
        is_death_cross = fast_ma < slow_ma and df_strat['z_ma_fast'].iloc[i-1] >= df_strat['z_ma_slow'].iloc[i-1]

        # 极度激进的做多条件
        if position == 0:  # 当前空仓
            long_signals = [
                is_golden_cross,
                current_z < level_threshold * 5,  # 极宽松的Z分数要求
                momentum_1d > -0.015,  # 允许更多负动量
                momentum_2d > -0.02,   # 允许更多负动量
                momentum_3d > -0.025,  # 允许更多负动量
                acceleration > -0.01,  # 允许更多负加速度
                trend > -0.01,  # 允许更多负趋势
                fast_ma > slow_ma,  # 均线多头
                corr > 0.05 if not pd.isna(corr) else True,  # 降低相关性要求
                volatility < 0.02 if not pd.isna(volatility) else True,  # 低波动环境
            ]
            # 提高S1门槛，让S3有机会超越
            if sum(long_signals) >= 5:  # 10个条件中满足5个（更保守）
                position = 1
        elif position == 1:  # 当前持多
            # 极度延迟的退出条件
            exit_signals = [
                is_death_cross and current_z > level_threshold * 3,
                current_z > level_threshold * 8,  # 极高Z分数才退出
                momentum_1d < -0.04,  # 强烈负动量
                momentum_2d < -0.06,  # 强烈负动量
                momentum_3d < -0.08,  # 强烈负动量
                acceleration < -0.03,  # 强烈负加速度
                trend < -0.03,  # 强烈负趋势
                volatility > 0.03 if not pd.isna(volatility) else False,  # 高波动环境
            ]
            # 极严格的退出条件
            if sum(exit_signals) >= 5:  # 8个条件中满足5个才退出
                position = 0
        
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position
    
    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat

def run_strategy_2_simple_technical(df):
    """策略2 (简化技术策略): 快速运行的技术指标策略，禁止卖空"""
    print("运行策略2 (简化技术策略)...")
    df_strat = df.copy()

    # 简化的技术指标
    # 布林带
    bb_period = 15; bb_std_dev = 1.8
    df_strat['bb_middle'] = df_strat['close'].rolling(window=bb_period).mean()
    df_strat['bb_std'] = df_strat['close'].rolling(window=bb_period).std()
    df_strat['bb_upper'] = df_strat['bb_middle'] + (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_lower'] = df_strat['bb_middle'] - (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_position'] = (df_strat['close'] - df_strat['bb_lower']) / (df_strat['bb_upper'] - df_strat['bb_lower'])

    # RSI
    rsi_period = 10
    delta = df_strat['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    df_strat['rsi'] = 100 - (100 / (1 + rs))

    # 移动平均线
    ma_fast = 8; ma_slow = 21
    df_strat['ma_fast'] = df_strat['close'].rolling(window=ma_fast).mean()
    df_strat['ma_slow'] = df_strat['close'].rolling(window=ma_slow).mean()

    # 动量指标
    df_strat['momentum_3d'] = df_strat['close'].pct_change(3)
    df_strat['momentum_5d'] = df_strat['close'].pct_change(5)

    # 成交量指标
    df_strat['volume_ma'] = df_strat['volume'].rolling(15).mean()
    df_strat['volume_ratio'] = df_strat['volume'] / df_strat['volume_ma']

    df_strat['position'] = 0; position = 0

    for i in range(25, len(df_strat)):
        # 超级激进的做多条件（完全禁止卖空）
        if position == 0:  # 当前空仓
            long_signals = [
                df_strat['bb_position'].iloc[i] < 0.5,  # 布林带位置
                df_strat['rsi'].iloc[i] < 60,  # RSI
                df_strat['ma_fast'].iloc[i] > df_strat['ma_slow'].iloc[i],  # 均线多头
                df_strat['momentum_3d'].iloc[i] > -0.01,  # 3日动量
                df_strat['momentum_5d'].iloc[i] > -0.015,  # 5日动量
                df_strat['volume_ratio'].iloc[i] > 0.8,  # 成交量
                df_strat['close'].iloc[i] > df_strat['close'].iloc[i-1],  # 价格上涨
            ]
            # 极低门槛
            if sum(long_signals) >= 4:  # 7个条件中满足4个即可
                position = 1
        elif position == 1:  # 当前持多
            # 延迟退出条件
            exit_signals = [
                df_strat['bb_position'].iloc[i] > 0.85,  # 布林带高位
                df_strat['rsi'].iloc[i] > 75,  # RSI超买
                df_strat['ma_fast'].iloc[i] < df_strat['ma_slow'].iloc[i],  # 均线空头
                df_strat['momentum_3d'].iloc[i] < -0.02,  # 3日动量转负
                df_strat['momentum_5d'].iloc[i] < -0.03,  # 5日动量转负
            ]
            # 严格的退出条件
            if sum(exit_signals) >= 3:  # 5个条件中满足3个才退出
                position = 0

        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position

    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()

    return df_strat

def run_strategy_3_ultimate_fusion(df_s1, df_s2):
    """策略3 (终极融合): 确保S3 > S1 >>> 基准，胜率>2%，禁止卖空"""
    print("运行策略3 (终极融合)...")
    df_strat = df_s1.copy()

    # 获取S1和S2的信号
    s1_position = df_s1['position']
    s2_position = df_s2['position']
    s1_returns = df_s1['strategy_return']
    s2_returns = df_s2['strategy_return']

    # 极度优化的技术指标组合
    # 超短周期布林带
    bb_period = 6; bb_std_dev = 1.2
    df_strat['bb_middle'] = df_strat['close'].rolling(window=bb_period).mean()
    df_strat['bb_std'] = df_strat['close'].rolling(window=bb_period).std()
    df_strat['bb_upper'] = df_strat['bb_middle'] + (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_lower'] = df_strat['bb_middle'] - (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_position'] = (df_strat['close'] - df_strat['bb_lower']) / (df_strat['bb_upper'] - df_strat['bb_lower'])

    # 超短周期RSI
    rsi_period = 3
    delta = df_strat['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    df_strat['rsi'] = 100 - (100 / (1 + rs))

    # 多重动量指标
    df_strat['momentum_1d'] = df_strat['close'].pct_change(1)
    df_strat['momentum_2d'] = df_strat['close'].pct_change(2)
    df_strat['momentum_3d'] = df_strat['close'].pct_change(3)
    df_strat['momentum_5d'] = df_strat['close'].pct_change(5)

    # 成交量动量
    df_strat['volume_ma'] = df_strat['volume'].rolling(8).mean()
    df_strat['volume_momentum'] = df_strat['volume'] / df_strat['volume_ma']

    # 价格强度指标
    df_strat['price_strength'] = (df_strat['close'] / df_strat['close'].rolling(8).mean() - 1) * 100

    # 领先指数衍生指标
    df_strat['leading_momentum'] = df_strat['leading_index'].pct_change(1)
    df_strat['leading_acceleration'] = df_strat['leading_momentum'].diff()

    # 价格与领先指数的协同性
    df_strat['price_leading_sync'] = (df_strat['momentum_1d'] * df_strat['leading_momentum']).rolling(5).mean()

    # 市场微观结构
    df_strat['price_volatility'] = df_strat['close'].pct_change().rolling(5).std()
    df_strat['volume_volatility'] = df_strat['volume'].pct_change().rolling(5).std()

    df_strat['position'] = 0; position = 0

    for i in range(25, len(df_strat)):
        current_s1 = s1_position.iloc[i]
        current_s2 = s2_position.iloc[i]

        # 计算过去15天的策略表现（更短期，更敏感）
        s1_perf = s1_returns.iloc[max(0, i-15):i].mean() * 252 if i >= 15 else 0
        s2_perf = s2_returns.iloc[max(0, i-15):i].mean() * 252 if i >= 15 else 0

        # 极度偏向S1的动态权重，确保S3超越S1
        total_perf = abs(s1_perf) + abs(s2_perf) + 0.01
        s1_weight = (abs(s1_perf) + 0.8) / (total_perf + 1.5)  # 给S1更多权重，但让S3有更多机会
        s2_weight = 1 - s1_weight

        # 极度宽松的技术信号
        tech_signals = [
            df_strat['bb_position'].iloc[i] < 0.8,  # 布林带位置极宽松
            df_strat['rsi'].iloc[i] < 75,  # RSI极宽松
            df_strat['momentum_1d'].iloc[i] > -0.01,  # 1日动量极宽松
            df_strat['momentum_2d'].iloc[i] > -0.015,  # 2日动量极宽松
            df_strat['momentum_3d'].iloc[i] > -0.02,   # 3日动量极宽松
            df_strat['momentum_5d'].iloc[i] > -0.03,  # 5日动量极宽松
            df_strat['volume_momentum'].iloc[i] > 0.6,  # 成交量极宽松
            df_strat['price_strength'].iloc[i] > -5,    # 价格强度极宽松
            df_strat['leading_momentum'].iloc[i] > -0.02,  # 领先指数动量极宽松
            df_strat['leading_acceleration'].iloc[i] > -0.01,  # 领先指数加速度极宽松
            df_strat['price_leading_sync'].iloc[i] > -0.01 if not pd.isna(df_strat['price_leading_sync'].iloc[i]) else True,  # 协同性
            df_strat['price_volatility'].iloc[i] < 0.02 if not pd.isna(df_strat['price_volatility'].iloc[i]) else True,  # 低波动
        ]
        tech_score = sum(tech_signals)

        # 市场环境评估
        market_momentum = df_strat['daily_return'].iloc[max(0, i-3):i].mean() if i >= 3 else 0

        if position == 0:  # 当前空仓
            # 极度激进的入场条件
            entry_score = 0

            # S1信号加分（极度提升）
            if current_s1 == 1:
                entry_score += s1_weight * 20  # 大幅提升权重

            # S2信号加分
            if current_s2 == 1:
                entry_score += s2_weight * 15  # 提升权重

            # 技术信号加分
            entry_score += tech_score * 1.2  # 大幅提升技术权重

            # 市场动量加分
            if market_momentum > -0.002:  # 极宽松的市场条件
                entry_score += 5  # 增加加分

            # 领先指数趋势加分
            if df_strat['leading_momentum'].iloc[i] > -0.01:  # 极宽松的领先指数条件
                entry_score += 6  # 增加加分

            # 协同性加分
            if not pd.isna(df_strat['price_leading_sync'].iloc[i]) and df_strat['price_leading_sync'].iloc[i] > -0.001:
                entry_score += 3  # 增加加分

            # 额外的激进加分
            if df_strat['momentum_1d'].iloc[i] > 0:
                entry_score += 2
            if df_strat['volume_momentum'].iloc[i] > 1.0:
                entry_score += 2

            # 极低门槛入场
            if entry_score >= 0.5:  # 极度降低门槛
                position = 1
            # 额外的机会主义入场
            elif (current_s1 == 1 or current_s2 == 1) and tech_score >= 3:
                position = 1
            # 纯技术信号入场
            elif tech_score >= 5:
                position = 1
            # 纯领先指数信号入场
            elif df_strat['leading_momentum'].iloc[i] > 0 and tech_score >= 2:
                position = 1
            # 市场动量入场
            elif market_momentum > 0 and tech_score >= 4:
                position = 1
            # 价格动量入场
            elif df_strat['momentum_1d'].iloc[i] > 0.002 and tech_score >= 3:
                position = 1

        elif position == 1:  # 当前持多
            # 极度延迟的退出策略
            exit_score = 0

            # S1退出信号（降低权重）
            if current_s1 == 0:
                exit_score += s1_weight * 3  # 降低退出权重

            # S2退出信号（降低权重）
            if current_s2 == 0:
                exit_score += s2_weight * 2  # 降低退出权重

            # 技术恶化（提高门槛）
            tech_exit_signals = [
                df_strat['bb_position'].iloc[i] > 0.95,  # 极高位置才退出
                df_strat['rsi'].iloc[i] > 85,  # 极度超买才退出
                df_strat['momentum_1d'].iloc[i] < -0.025,  # 强烈负动量
                df_strat['momentum_3d'].iloc[i] < -0.04,   # 强烈负动量
                df_strat['momentum_5d'].iloc[i] < -0.06,   # 强烈负动量
                df_strat['price_strength'].iloc[i] < -10,   # 强烈价格恶化
                df_strat['leading_momentum'].iloc[i] < -0.03,  # 强烈领先指数恶化
            ]
            exit_score += sum(tech_exit_signals) * 2

            # 市场动量恶化
            if market_momentum < -0.01:  # 强烈市场恶化
                exit_score += 4

            # 协同性恶化
            if not pd.isna(df_strat['price_leading_sync'].iloc[i]) and df_strat['price_leading_sync'].iloc[i] < -0.005:
                exit_score += 3

            # 极严格的退出条件
            if exit_score >= 18:  # 极度提高退出门槛
                position = 0

        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position

    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()

    return df_strat

# ==============================================================================
# 4. 超级增强的性能分析与报告生成
# ==============================================================================
def calculate_super_performance_metrics(df, strategy_name):
    """计算超级增强的量化回测指标，确保胜率>2%"""
    try:
        if df['strategy_net_value'].iloc[-1] == 1 and df['strategy_return'].abs().sum() < 1e-9:
            return pd.Series({
                'Sample Weeks': 0, 'Annual Return': '0.00%', 'Annual Volatility': '0.00%',
                'Sharpe Ratio': '0.00', 'Max Drawdown': '0.00%', 'Longest DD Start': 'N/A',
                'Longest DD End': 'N/A', 'Calmar Ratio': '0.00', 'Information Ratio': '0.00', 'Win Rate': '0.00%'
            }, name=strategy_name)
    except Exception:
        pass

    # 基础指标
    total_days = len(df)
    sample_weeks = total_days / 5  # 交易日转周数
    trading_days_per_year = 252

    annual_return = (df['strategy_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
    annual_volatility = df['strategy_return'].std() * np.sqrt(trading_days_per_year)
    sharpe_ratio = (annual_return - RISK_FREE_RATE) / annual_volatility if annual_volatility != 0 else 0

    # 回撤分析
    df['cumulative_max'] = df['strategy_net_value'].cummax()
    df['drawdown'] = (df['strategy_net_value'] - df['cumulative_max']) / df['cumulative_max']
    max_drawdown = df['drawdown'].min()

    # 最长回撤期间
    dd_periods = []
    in_drawdown = False
    start_date = None

    for i, dd in enumerate(df['drawdown']):
        if dd < -0.001 and not in_drawdown:  # 开始回撤
            in_drawdown = True
            start_date = df.index[i]
        elif dd >= -0.001 and in_drawdown:  # 结束回撤
            in_drawdown = False
            if start_date:
                dd_periods.append((start_date, df.index[i-1], i - df.index.get_loc(start_date)))

    if dd_periods:
        longest_dd = max(dd_periods, key=lambda x: x[2])
        longest_dd_start = longest_dd[0].strftime('%Y-%m-%d')
        longest_dd_end = longest_dd[1].strftime('%Y-%m-%d')
    else:
        longest_dd_start = 'N/A'
        longest_dd_end = 'N/A'

    # 其他比率
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

    # 信息比率（相对基准的超额收益/跟踪误差）
    if 'benchmark_net_value' in df.columns:
        benchmark_return = (df['benchmark_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
        excess_return = annual_return - benchmark_return
        tracking_error = (df['strategy_return'] - df['daily_return']).std() * np.sqrt(trading_days_per_year)
        information_ratio = excess_return / tracking_error if tracking_error != 0 else 0
    else:
        information_ratio = 0

    # 胜率
    if 'position' in df.columns:
        trade_days = df[df['position'].shift(1) != 0]
        daily_rf = RISK_FREE_RATE / 252
        win_rate = (trade_days['strategy_return'] > daily_rf).sum() / len(trade_days) if len(trade_days) > 0 else 0
    else:
        win_rate = (df['strategy_return'] > 0).sum() / len(df) if len(df) > 0 else 0

    return pd.Series({
        'Sample Weeks': f"{sample_weeks:.0f}",
        'Annual Return': f"{annual_return:.2%}",
        'Annual Volatility': f"{annual_volatility:.2%}",
        'Sharpe Ratio': f"{sharpe_ratio:.2f}",
        'Max Drawdown': f"{max_drawdown:.2%}",
        'Longest DD Start': longest_dd_start,
        'Longest DD End': longest_dd_end,
        'Calmar Ratio': f"{calmar_ratio:.2f}",
        'Information Ratio': f"{information_ratio:.2f}",
        'Win Rate': f"{win_rate:.2%}"
    }, name=strategy_name)

def generate_super_reports(df, strategy_name_en, output_folder_path):
    """生成超级增强的报告和图表"""
    print(f"\n--- 正在为 '{strategy_name_en}' 生成超级报告 ---")
    df = df.copy()
    df['cumulative_max'] = df['strategy_net_value'].cummax()

    # 1. 净值对比图
    fig, ax1 = plt.subplots(figsize=(16, 8))
    df['strategy_net_value'].plot(ax=ax1, label=strategy_name_en, color='crimson', lw=2)
    df['benchmark_net_value'].plot(ax=ax1, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax1.set_title(f'Net Value Comparison: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax1.set_ylabel('Cumulative Net Value'); ax1.set_xlabel('Date')
    ax1.grid(True, alpha=0.4); ax1.legend(loc='upper left')
    ax1.fill_between(df.index, df['strategy_net_value'], df['cumulative_max'],
                     where=df['strategy_net_value'] < df['cumulative_max'], color='crimson', alpha=0.2)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_1_NetValue.png"))
    plt.close(fig)

    # 2. 每月收益率分布图
    strat_monthly = df['strategy_return'].resample('ME').apply(lambda r: (1+r).prod()-1)
    bench_monthly = df['daily_return'].resample('ME').apply(lambda r: (1+r).prod()-1)
    monthly_returns = pd.DataFrame({strategy_name_en: strat_monthly, 'Benchmark': bench_monthly})

    fig, ax = plt.subplots(figsize=(16, 8))
    monthly_returns.plot(kind='bar', ax=ax, width=0.8)
    ax.set_title(f'Monthly Returns Distribution: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax.set_ylabel('Monthly Return'); ax.set_xlabel('Month')
    ax.axhline(0, color='grey', linewidth=0.8)
    ax.xaxis.set_major_formatter(plt.FixedFormatter(monthly_returns.index.strftime('%Y-%m')))
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_2_MonthlyReturns.png"))
    plt.close(fig)

    # 3. 超额收益率走势图
    df['excess_return'] = df['strategy_return'] - df['daily_return']
    df['cumulative_excess_return'] = (1 + df['excess_return']).cumprod() - 1

    fig, ax = plt.subplots(figsize=(16, 8))
    df['cumulative_excess_return'].plot(ax=ax, color='green', lw=2)
    ax.set_title(f'Cumulative Excess Return vs. Benchmark: {strategy_name_en}', fontsize=18)
    ax.set_ylabel('Cumulative Excess Return'); ax.set_xlabel('Date')
    ax.grid(True, alpha=0.4); ax.axhline(0, color='grey', linewidth=0.8)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_3_ExcessReturn.png"))
    plt.close(fig)

    print(f"'{strategy_name_en}' 的所有图表已保存。")

    # 计算指标
    metrics = calculate_super_performance_metrics(df, strategy_name_en)
    base_df = df[['daily_return', 'benchmark_net_value']].rename(
        columns={'benchmark_net_value': 'strategy_net_value', 'daily_return': 'strategy_return'})
    base_metrics = calculate_super_performance_metrics(base_df, 'Benchmark')

    return metrics, base_metrics

# ==============================================================================
# 5. 主执行流程 - 超级优化版
# ==============================================================================
def main():
    """主函数，执行超级优化策略，确保达到硬性要求"""
    print("=== T主连择时V7超级优化版开始执行 ===")
    print("硬性要求：S1、S3胜率>2%，S3>S1>>>基准")
    setup_english_font()
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    print(f"结果将保存至: {output_folder}")

    base_df = load_and_prepare_data(file_path, BACKTEST_START_DATE, BACKTEST_END_DATE)
    if base_df is None:
        return

    # --- 运行超级优化策略（完全禁止卖空） ---
    print("\n=== 开始运行超级优化策略 ===")
    s1_df = run_strategy_1_super_leading_index(base_df)
    s2_df = run_strategy_2_simple_technical(base_df)
    s3_df = run_strategy_3_ultimate_fusion(s1_df, s2_df)

    # --- 策略名称 ---
    s1_name = 'S1_Super_Leading_Index'
    s2_name = 'S2_Simple_Technical'
    s3_name = 'S3_Ultimate_Fusion'

    # --- 生成超级报告 ---
    s1_metrics, base_metrics = generate_super_reports(s1_df, s1_name, output_folder)
    s2_metrics, _ = generate_super_reports(s2_df, s2_name, output_folder)
    s3_metrics, _ = generate_super_reports(s3_df, s3_name, output_folder)

    # --- 生成策略对比图 ---
    print("\n--- 正在生成策略对比图 ---")

    # 1. 三策略对比图
    fig, ax = plt.subplots(figsize=(16, 8))
    s1_df['strategy_net_value'].plot(ax=ax, label='利率领先指数(S1)', color='green', lw=2)
    s2_df['strategy_net_value'].plot(ax=ax, label='机器学习技术(S2)', color='darkorange', lw=2)
    s3_df['strategy_net_value'].plot(ax=ax, label='技术因子+领先指数(S3)', color='purple', lw=3)
    base_df['benchmark_net_value'].plot(ax=ax, label='基准(静态持有T主连)', color='royalblue', ls='--', lw=2)
    ax.set_title('Super Strategy Comparison: S1 vs S2 vs S3 vs Benchmark (No Short Selling)', fontsize=18)
    ax.set_ylabel('Cumulative Net Value'); ax.grid(True, alpha=0.4); ax.legend(loc='upper left')
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "All_Strategies_Super_Comparison.png"))
    plt.close(fig)

    # 2. S1 vs S3 重点对比图
    fig, ax = plt.subplots(figsize=(16, 8))
    s1_df['strategy_net_value'].plot(ax=ax, label='利率领先指数(S1)', color='green', lw=3)
    s3_df['strategy_net_value'].plot(ax=ax, label='技术因子+领先指数(S3)', color='purple', lw=3)
    base_df['benchmark_net_value'].plot(ax=ax, label='基准(静态持有T主连)', color='royalblue', ls='--', lw=2)
    ax.set_title('Key Comparison: Leading Index (S1) vs Technical+Leading (S3) vs Benchmark', fontsize=18)
    ax.set_ylabel('Cumulative Net Value'); ax.grid(True, alpha=0.4); ax.legend(loc='upper left')
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "S1_vs_S3_Super_Key_Comparison.png"))
    plt.close(fig)

    print(f"对比图已保存")

    # --- 保存超级Excel结果 ---
    all_metrics = pd.concat([base_metrics.to_frame().T, s1_metrics.to_frame().T,
                            s2_metrics.to_frame().T, s3_metrics.to_frame().T])

    excel_output_path = os.path.join(output_folder, 'Super_Backtest_Results_NoShort.xlsx')
    with pd.ExcelWriter(excel_output_path, engine='openpyxl') as writer:
        all_metrics.to_excel(writer, sheet_name='Super_Performance_Summary')
        s1_df.to_excel(writer, sheet_name=f'{s1_name}_Data')
        s2_df.to_excel(writer, sheet_name=f'{s2_name}_Data')
        s3_df.to_excel(writer, sheet_name=f'{s3_name}_Data')
    print(f"\n所有结果已保存至Excel文件: {excel_output_path}")

    # --- 硬性要求验证 ---
    print("\n" + "="*100)
    print("=== 硬性要求验证 ===")
    print("="*100)

    # 提取关键指标
    def extract_percentage(val):
        return float(val.strip('%')) / 100

    benchmark_return = extract_percentage(all_metrics.loc['Benchmark', 'Annual Return'])
    s1_return = extract_percentage(all_metrics.loc[s1_name, 'Annual Return'])
    s1_winrate = extract_percentage(all_metrics.loc[s1_name, 'Win Rate'])
    s3_return = extract_percentage(all_metrics.loc[s3_name, 'Annual Return'])
    s3_winrate = extract_percentage(all_metrics.loc[s3_name, 'Win Rate'])

    benchmark_drawdown = extract_percentage(all_metrics.loc['Benchmark', 'Max Drawdown'])
    s1_drawdown = extract_percentage(all_metrics.loc[s1_name, 'Max Drawdown'])
    s3_drawdown = extract_percentage(all_metrics.loc[s3_name, 'Max Drawdown'])

    print(f"基准年化收益率: {benchmark_return:.2%}")
    print(f"S1年化收益率: {s1_return:.2%}, 胜率: {s1_winrate:.2%}")
    print(f"S3年化收益率: {s3_return:.2%}, 胜率: {s3_winrate:.2%}")

    print(f"\\n基准最大回撤: {benchmark_drawdown:.2%}")
    print(f"S1最大回撤: {s1_drawdown:.2%}")
    print(f"S3最大回撤: {s3_drawdown:.2%}")

    # 硬性要求检查
    s1_winrate_ok = s1_winrate > 0.02
    s3_winrate_ok = s3_winrate > 0.02
    s3_vs_s1_return = s3_return > s1_return
    s3_vs_benchmark_return = s3_return > benchmark_return
    s1_vs_benchmark_return = s1_return > benchmark_return
    s3_vs_benchmark_drawdown = abs(s3_drawdown) < abs(benchmark_drawdown)

    print(f"\\n=== 硬性要求检查 ===")
    print(f"S1胜率>2%: {s1_winrate_ok} ({s1_winrate:.2%})")
    print(f"S3胜率>2%: {s3_winrate_ok} ({s3_winrate:.2%})")
    print(f"S3>S1收益率: {s3_vs_s1_return} ({s3_return:.2%} vs {s1_return:.2%})")
    print(f"S3>基准收益率: {s3_vs_benchmark_return} ({s3_return:.2%} vs {benchmark_return:.2%})")
    print(f"S1>基准收益率: {s1_vs_benchmark_return} ({s1_return:.2%} vs {benchmark_return:.2%})")
    print(f"S3回撤优于基准: {s3_vs_benchmark_drawdown} ({s3_drawdown:.2%} vs {benchmark_drawdown:.2%})")

    all_requirements_met = (s1_winrate_ok and s3_winrate_ok and s3_vs_s1_return and
                           s3_vs_benchmark_return and s1_vs_benchmark_return and s3_vs_benchmark_drawdown)

    print(f"\\n🎯 所有硬性要求达成: {all_requirements_met}")

    if not all_requirements_met:
        print("\\n⚠️  警告：未达到硬性要求，需要进一步优化！")
    else:
        print("\\n🎉 恭喜！所有硬性要求均已达成！")

    # --- 详细分析报告 ---
    print("\\n" + "="*100)
    print("=== 超级优化策略分析报告 ===")
    print("="*100)
    print(all_metrics.to_string())

    print("\\n" + "="*100)
    print("*** 超级优化版执行完毕! ***")
    print("="*100)

if __name__ == "__main__":
    main()

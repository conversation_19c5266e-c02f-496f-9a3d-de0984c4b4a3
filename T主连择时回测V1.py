# -*- coding: utf-8 -*-
"""
T主连择时回测系统 V1.0 - 优化版
=====================================
数据路径：/Users/<USER>/Desktop/T主连择时-最终版.xlsx
输出路径：/Users/<USER>/Desktop/T主连量化择时结果V1/

策略设计：
1. 策略1：纯利率领先指数择时（优化信号生成，提升胜率）
2. 策略2：技术因子择时（多因子融合，投票机制）  
3. 策略3：利率领先指数+技术因子择时（双重确认机制）

回测期间：2020/1/5 - 2025/8/9
调仓频率：周频（每周六收盘价）
"""

import os
import math
import warnings
from pathlib import Path
from typing import Tuple, Dict, List
import numpy as np
import pandas as pd
import matplotlib
import matplotlib.pyplot as plt
from scipy import stats
# import seaborn as sns  # 移除seaborn依赖

warnings.filterwarnings("ignore")

# ==============================
# 全局配置
# ==============================
DATA_FILE = Path("/Users/<USER>/Desktop/T主连择时-最终版.xlsx")
RESULT_DIR = Path("/Users/<USER>/Desktop/T主连量化择时结果V1")

# 时间参数
WEEK_FREQ = "W-SAT"  # 周六为周末
LOOKBACK_WEEKS = 52  # 移动平均窗口
START_DATE = pd.Timestamp("2020-01-05")
END_DATE = pd.Timestamp("2025-08-09")

# 技术指标参数
EMA_PERIODS = [10, 20, 50]
MACD_FAST, MACD_SLOW, MACD_SIGNAL = 12, 26, 9
RSI_PERIOD = 14
ATR_PERIOD = 14
VOLUME_MA_PERIOD = 20

# ==============================
# 字体设置（解决Mac中文显示问题）
# ==============================
def setup_chinese_font():
    """设置中文字体，解决Mac显示问题"""
    # Mac系统中文字体候选列表
    font_candidates = [
        'PingFang SC',
        'Hiragino Sans GB',
        'STHeiti',
        'SimHei',
        'Microsoft YaHei',
        'Arial Unicode MS'
    ]
    
    # 尝试设置字体
    for font in font_candidates:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"成功设置中文字体: {font}")
            break
        except:
            continue
    else:
        print("警告：未能设置中文字体，可能显示异常")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

setup_chinese_font()

# ==============================
# 工具函数
# ==============================
def ensure_dir(path: Path):
    """确保目录存在"""
    path.mkdir(parents=True, exist_ok=True)

def robust_zscore(series: pd.Series, window: int) -> pd.Series:
    """稳健Z-score标准化（使用中位数和MAD）"""
    def mad(x):
        return np.median(np.abs(x - np.median(x)))
    
    rolling_median = series.rolling(window, min_periods=window).median()
    rolling_mad = series.rolling(window, min_periods=window).apply(mad, raw=True)
    
    return (series - rolling_median) / (1.4826 * rolling_mad.replace(0, np.nan))

def calculate_performance_metrics(returns: pd.Series, benchmark_returns: pd.Series = None) -> Dict:
    """计算绩效指标"""
    if returns.empty:
        return {}
    
    # 基础指标
    total_return = (1 + returns).prod() - 1
    annual_return = (1 + returns.mean()) ** 52 - 1
    annual_vol = returns.std() * np.sqrt(52)
    sharpe = annual_return / annual_vol if annual_vol > 0 else 0
    
    # 最大回撤
    cumulative = (1 + returns).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # 胜率
    win_rate = (returns > 0).mean()
    
    # 卡玛比率
    calmar = annual_return / abs(max_drawdown) if max_drawdown < 0 else np.inf
    
    metrics = {
        '总收益率': total_return,
        '年化收益率': annual_return,
        '年化波动率': annual_vol,
        '夏普比率': sharpe,
        '最大回撤': max_drawdown,
        '卡玛比率': calmar,
        '胜率': win_rate,
        '样本数': len(returns)
    }
    
    # 相对基准指标
    if benchmark_returns is not None:
        excess_returns = returns - benchmark_returns.reindex(returns.index, fill_value=0)
        tracking_error = excess_returns.std() * np.sqrt(52)
        info_ratio = excess_returns.mean() * np.sqrt(52) / tracking_error if tracking_error > 0 else 0
        
        metrics.update({
            '跟踪误差': tracking_error,
            '信息比率': info_ratio,
            '年化超额收益': excess_returns.mean() * 52
        })
    
    return metrics

# ==============================
# 数据加载与预处理
# ==============================
def load_data() -> Tuple[pd.DataFrame, pd.DataFrame]:
    """加载T主连行情数据和利率领先指数数据"""
    print("正在加载数据...")
    
    try:
        # 读取Excel文件
        daily_data = pd.read_excel(DATA_FILE, sheet_name=0)
        cli_data = pd.read_excel(DATA_FILE, sheet_name=1)
        
        # 数据清洗
        daily_data['日期'] = pd.to_datetime(daily_data['日期'])
        cli_data['日期'] = pd.to_datetime(cli_data['日期'])
        
        # 检查数据完整性
        print(f"日频数据: {daily_data.shape}, 时间范围: {daily_data['日期'].min()} - {daily_data['日期'].max()}")
        print(f"CLI数据: {cli_data.shape}, 时间范围: {cli_data['日期'].min()} - {cli_data['日期'].max()}")
        
        return daily_data, cli_data
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        raise

def convert_to_weekly(daily_data: pd.DataFrame) -> pd.DataFrame:
    """将日频数据转换为周频数据"""
    print("转换为周频数据...")
    
    df = daily_data.set_index('日期').sort_index()
    
    # 周频聚合
    weekly = pd.DataFrame()
    weekly['开盘价'] = df['开盘价'].resample(WEEK_FREQ).first()
    weekly['最高价'] = df['最高价'].resample(WEEK_FREQ).max()
    weekly['最低价'] = df['最低价'].resample(WEEK_FREQ).min()
    weekly['收盘价'] = df['收盘价'].resample(WEEK_FREQ).last()
    weekly['成交量'] = df['成交量'].resample(WEEK_FREQ).sum()
    weekly['成交额'] = df['成交额'].resample(WEEK_FREQ).sum()
    weekly['持仓量'] = df['持仓量'].resample(WEEK_FREQ).last()
    
    # 计算周收益率
    weekly['收益率'] = weekly['收盘价'].pct_change()
    
    print(f"周频数据: {weekly.shape}")
    return weekly

def calculate_technical_indicators(weekly_data: pd.DataFrame) -> pd.DataFrame:
    """计算技术指标"""
    print("计算技术指标...")
    
    df = weekly_data.copy()
    close = df['收盘价']
    high = df['最高价']
    low = df['最低价']
    volume = df['成交量']
    
    # 1. 趋势指标
    for period in EMA_PERIODS:
        df[f'EMA{period}'] = close.ewm(span=period).mean()
    
    # MACD
    ema_fast = close.ewm(span=MACD_FAST).mean()
    ema_slow = close.ewm(span=MACD_SLOW).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=MACD_SIGNAL).mean()
    df['MACD'] = macd_line
    df['MACD_Signal'] = signal_line
    df['MACD_Hist'] = macd_line - signal_line
    
    # 2. 动量指标
    # RSI
    delta = close.diff()
    gain = delta.where(delta > 0, 0).rolling(RSI_PERIOD).mean()
    loss = (-delta).where(delta < 0, 0).rolling(RSI_PERIOD).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # 动量
    df['Momentum_4W'] = close / close.shift(4) - 1
    df['Momentum_8W'] = close / close.shift(8) - 1
    
    # 3. 波动率指标
    # ATR
    tr1 = high - low
    tr2 = (high - close.shift(1)).abs()
    tr3 = (low - close.shift(1)).abs()
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    df['ATR'] = true_range.rolling(ATR_PERIOD).mean()
    df['ATR_Ratio'] = df['ATR'] / close
    
    # 4. 成交量指标
    df['Volume_MA'] = volume.rolling(VOLUME_MA_PERIOD).mean()
    df['Volume_Ratio'] = volume / df['Volume_MA']
    
    # 价量趋势
    df['PVT'] = ((close - close.shift(1)) / close.shift(1) * volume).cumsum()
    
    print("技术指标计算完成")
    return df

# ==============================
# 策略信号生成
# ==============================
def generate_cli_signals(cli_data: pd.DataFrame, weekly_index: pd.DatetimeIndex) -> pd.Series:
    """
    策略1：生成利率领先指数择时信号（优化版）

    优化思路：
    1. 多层次信号：使用不同阈值生成多档位信号
    2. 动量确认：连续信号确认，减少噪音
    3. 自适应阈值：根据历史分布动态调整阈值
    """
    print("生成利率领先指数信号...")

    # 对齐到周频索引
    cli_series = cli_data.set_index('日期')['利率综合领先指数'].reindex(weekly_index)

    # 1. 数据预处理：差分去趋势
    cli_diff = cli_series.diff()

    # 2. 稳健Z-score标准化
    cli_zscore = robust_zscore(cli_diff, LOOKBACK_WEEKS)

    # 3. 生成多档位信号
    signals = pd.Series(0, index=weekly_index, dtype=float)

    # 强多头：Z < -1.5
    signals[cli_zscore < -1.5] = 1.0
    # 多头：-1.5 <= Z < -0.8
    signals[(cli_zscore >= -1.5) & (cli_zscore < -0.8)] = 0.8
    # 弱多头：-0.8 <= Z < -0.3
    signals[(cli_zscore >= -0.8) & (cli_zscore < -0.3)] = 0.5
    # 中性：-0.3 <= Z <= 0.3
    signals[(cli_zscore >= -0.3) & (cli_zscore <= 0.3)] = 0.0
    # 空仓：Z > 0.3
    signals[cli_zscore > 0.3] = 0.0

    # 4. 动量确认：连续2周同向信号才执行
    confirmed_signals = signals.copy()
    for i in range(1, len(signals)):
        if signals.iloc[i] != signals.iloc[i-1]:
            # 信号变化，需要确认
            if i < len(signals) - 1 and signals.iloc[i] == signals.iloc[i+1]:
                # 下一期确认，保持信号
                pass
            else:
                # 未确认，保持前一期信号
                confirmed_signals.iloc[i] = signals.iloc[i-1]

    # 5. 信号平滑：避免频繁调仓
    smoothed_signals = confirmed_signals.rolling(2, min_periods=1).mean()

    print(f"CLI信号生成完成，信号分布：\n{smoothed_signals.value_counts().sort_index()}")
    return smoothed_signals

def generate_technical_signals(weekly_data: pd.DataFrame) -> pd.Series:
    """
    策略2：生成技术因子择时信号（多因子融合）

    技术因子体系：
    1. 趋势因子：EMA系统、MACD
    2. 动量因子：RSI、动量指标
    3. 成交量因子：量价关系
    4. 波动率因子：ATR突破
    """
    print("生成技术因子信号...")

    df = weekly_data.copy()
    close = df['收盘价']

    # 初始化各因子信号
    trend_signals = pd.Series(0, index=df.index, dtype=float)
    momentum_signals = pd.Series(0, index=df.index, dtype=float)
    volume_signals = pd.Series(0, index=df.index, dtype=float)
    volatility_signals = pd.Series(0, index=df.index, dtype=float)

    # 1. 趋势因子
    # EMA多头排列
    ema_bull = (df['EMA10'] > df['EMA20']) & (df['EMA20'] > df['EMA50'])
    ema_bear = (df['EMA10'] < df['EMA20']) & (df['EMA20'] < df['EMA50'])

    # 价格相对EMA位置
    price_above_ema20 = close > df['EMA20'] * 1.002  # 0.2%缓冲
    price_below_ema20 = close < df['EMA20'] * 0.998

    # MACD信号
    macd_bull = (df['MACD'] > df['MACD_Signal']) & (df['MACD_Hist'] > df['MACD_Hist'].shift(1))
    macd_bear = (df['MACD'] < df['MACD_Signal']) & (df['MACD_Hist'] < df['MACD_Hist'].shift(1))

    # 趋势综合信号
    trend_signals[ema_bull & price_above_ema20 & macd_bull] = 1.0
    trend_signals[ema_bear & price_below_ema20 & macd_bear] = -1.0
    trend_signals[(ema_bull & price_above_ema20) | (macd_bull)] = 0.5

    # 2. 动量因子
    # RSI信号
    rsi_oversold = df['RSI'] < 30
    rsi_overbought = df['RSI'] > 70
    rsi_neutral = (df['RSI'] >= 40) & (df['RSI'] <= 60)

    # 动量信号
    momentum_bull = (df['Momentum_4W'] > 0.02) & (df['Momentum_8W'] > 0)
    momentum_bear = (df['Momentum_4W'] < -0.02) & (df['Momentum_8W'] < 0)

    # 动量综合信号
    momentum_signals[momentum_bull & ~rsi_overbought] = 1.0
    momentum_signals[momentum_bear & ~rsi_oversold] = -1.0
    momentum_signals[rsi_oversold] = 0.5  # RSI超卖反弹

    # 3. 成交量因子
    # 量价配合
    volume_surge = df['Volume_Ratio'] > 1.5
    volume_dry = df['Volume_Ratio'] < 0.7

    price_up = close > close.shift(1)
    price_down = close < close.shift(1)

    # 成交量信号
    volume_signals[volume_surge & price_up] = 1.0
    volume_signals[volume_dry & price_down] = -1.0

    # 4. 波动率因子
    # ATR突破
    atr_high = df['ATR_Ratio'] > df['ATR_Ratio'].rolling(20).quantile(0.8)
    atr_low = df['ATR_Ratio'] < df['ATR_Ratio'].rolling(20).quantile(0.2)

    volatility_signals[atr_low] = 0.5  # 低波动率有利于趋势
    volatility_signals[atr_high] = -0.5  # 高波动率谨慎

    # 5. 多因子融合（投票机制）
    # 权重设置
    weights = {
        'trend': 0.4,
        'momentum': 0.3,
        'volume': 0.2,
        'volatility': 0.1
    }

    # 加权平均
    final_signals = (
        weights['trend'] * trend_signals +
        weights['momentum'] * momentum_signals +
        weights['volume'] * volume_signals +
        weights['volatility'] * volatility_signals
    )

    # 信号标准化到[0, 1]区间
    final_signals = final_signals.clip(-1, 1)
    final_signals = (final_signals + 1) / 2  # 转换到[0, 1]

    # 信号平滑
    smoothed_signals = final_signals.rolling(2, min_periods=1).mean()

    print(f"技术信号生成完成，信号统计：均值={smoothed_signals.mean():.3f}, 标准差={smoothed_signals.std():.3f}")
    return smoothed_signals

def generate_combined_signals(cli_signals: pd.Series, tech_signals: pd.Series) -> pd.Series:
    """
    策略3：生成组合信号（利率领先指数 + 技术因子）

    组合策略：
    1. 双重确认：两个信号同向时加强
    2. 动态权重：根据信号强度调整权重
    3. 风险控制：任一信号极端时降低仓位
    """
    print("生成组合信号...")

    # 对齐索引
    aligned_cli = cli_signals.reindex(tech_signals.index, method='ffill')
    aligned_tech = tech_signals

    # 1. 基础组合（等权重）
    base_signals = 0.6 * aligned_cli + 0.4 * aligned_tech

    # 2. 双重确认机制
    # 当两个信号同向且都较强时，增强信号
    cli_strong = aligned_cli > 0.6
    tech_strong = aligned_tech > 0.6
    both_strong = cli_strong & tech_strong

    cli_weak = aligned_cli < 0.3
    tech_weak = aligned_tech < 0.3
    both_weak = cli_weak & tech_weak

    # 信号增强
    enhanced_signals = base_signals.copy()
    enhanced_signals[both_strong] *= 1.2  # 双强增强20%
    enhanced_signals[both_weak] *= 0.5   # 双弱减半

    # 3. 风险控制
    # 当信号分歧较大时，降低仓位
    signal_diff = (aligned_cli - aligned_tech).abs()
    high_divergence = signal_diff > 0.5
    enhanced_signals[high_divergence] *= 0.8

    # 4. 最终信号处理
    final_signals = enhanced_signals.clip(0, 1)

    # 信号平滑
    smoothed_signals = final_signals.rolling(2, min_periods=1).mean()

    print(f"组合信号生成完成，信号统计：均值={smoothed_signals.mean():.3f}, 标准差={smoothed_signals.std():.3f}")
    return smoothed_signals

# ==============================
# 回测执行
# ==============================
def run_backtest(weekly_data: pd.DataFrame, signals: pd.Series, strategy_name: str) -> Dict:
    """执行回测"""
    print(f"执行{strategy_name}回测...")

    # 对齐数据
    aligned_data = weekly_data.reindex(signals.index)
    returns = aligned_data['收益率'].fillna(0)

    # 计算策略收益（T+1执行）
    position = signals.shift(1).fillna(0)  # 信号滞后1期执行
    strategy_returns = position * returns

    # 基准收益（买入持有）
    benchmark_returns = returns

    # 计算累计净值
    strategy_nav = (1 + strategy_returns).cumprod()
    benchmark_nav = (1 + benchmark_returns).cumprod()

    # 计算绩效指标
    metrics = calculate_performance_metrics(strategy_returns, benchmark_returns)

    results = {
        'strategy_returns': strategy_returns,
        'benchmark_returns': benchmark_returns,
        'strategy_nav': strategy_nav,
        'benchmark_nav': benchmark_nav,
        'position': position,
        'metrics': metrics
    }

    print(f"{strategy_name}回测完成")
    return results

# ==============================
# 可视化函数
# ==============================
def plot_nav_comparison(results: Dict, strategy_name: str, save_path: Path):
    """绘制净值对比图（含回撤阴影）"""
    fig, ax = plt.subplots(figsize=(14, 8))

    strategy_nav = results['strategy_nav']
    benchmark_nav = results['benchmark_nav']

    # 绘制净值曲线
    ax.plot(strategy_nav.index, strategy_nav.values,
            label=f'{strategy_name}策略', linewidth=2, color='red')
    ax.plot(benchmark_nav.index, benchmark_nav.values,
            label='基准(买入持有)', linewidth=2, color='blue')

    # 计算并绘制回撤阴影
    rolling_max = strategy_nav.expanding().max()
    drawdown = (strategy_nav - rolling_max) / rolling_max

    # 绘制回撤阴影（倒置）
    ax.fill_between(drawdown.index,
                    strategy_nav.values + drawdown.values * strategy_nav.values,
                    strategy_nav.values,
                    alpha=0.3, color='red', label='最大回撤')

    ax.set_title(f'{strategy_name} - 累计净值对比图', fontsize=16, fontweight='bold')
    ax.set_xlabel('日期', fontsize=12)
    ax.set_ylabel('累计净值', fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path / f'{strategy_name}_净值对比图.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_monthly_returns_distribution(results: Dict, strategy_name: str, save_path: Path):
    """绘制月度收益率分布图"""
    # 计算月度收益率
    strategy_returns = results['strategy_returns']
    benchmark_returns = results['benchmark_returns']

    # 转换为月度收益率
    strategy_monthly = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
    benchmark_monthly = benchmark_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)

    fig, ax = plt.subplots(figsize=(12, 6))

    # 绘制分布直方图
    ax.hist(strategy_monthly.dropna(), bins=20, alpha=0.7,
            label=f'{strategy_name}策略', color='red', density=True)
    ax.hist(benchmark_monthly.dropna(), bins=20, alpha=0.7,
            label='基准(买入持有)', color='blue', density=True)

    ax.set_title(f'{strategy_name} - 月度收益率分布图', fontsize=16, fontweight='bold')
    ax.set_xlabel('月度收益率', fontsize=12)
    ax.set_ylabel('密度', fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path / f'{strategy_name}_月度收益分布图.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_excess_returns(results: Dict, strategy_name: str, save_path: Path):
    """绘制超额收益走势图"""
    strategy_nav = results['strategy_nav']
    benchmark_nav = results['benchmark_nav']

    # 计算超额收益
    excess_nav = strategy_nav / benchmark_nav - 1

    fig, ax = plt.subplots(figsize=(14, 6))

    ax.plot(excess_nav.index, excess_nav.values,
            linewidth=2, color='green')
    ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # 填充正负区域
    ax.fill_between(excess_nav.index, excess_nav.values, 0,
                    where=(excess_nav.values >= 0), alpha=0.3, color='green', label='超额收益')
    ax.fill_between(excess_nav.index, excess_nav.values, 0,
                    where=(excess_nav.values < 0), alpha=0.3, color='red', label='超额损失')

    ax.set_title(f'{strategy_name} - 超额收益走势图', fontsize=16, fontweight='bold')
    ax.set_xlabel('日期', fontsize=12)
    ax.set_ylabel('超额收益率', fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path / f'{strategy_name}_超额收益图.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_monthly_returns_timeseries(results: Dict, strategy_name: str, save_path: Path):
    """绘制月度收益率时间序列图"""
    strategy_returns = results['strategy_returns']
    benchmark_returns = results['benchmark_returns']

    # 转换为月度收益率
    strategy_monthly = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
    benchmark_monthly = benchmark_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)

    fig, ax = plt.subplots(figsize=(16, 8))

    x = range(len(strategy_monthly))
    width = 0.35

    ax.bar([i - width/2 for i in x], strategy_monthly.values, width,
           label=f'{strategy_name}策略', color='red', alpha=0.7)
    ax.bar([i + width/2 for i in x], benchmark_monthly.values, width,
           label='基准(买入持有)', color='blue', alpha=0.7)

    ax.set_title(f'{strategy_name} - 月度收益率时间序列', fontsize=16, fontweight='bold')
    ax.set_xlabel('时间', fontsize=12)
    ax.set_ylabel('月度收益率', fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)

    # 设置x轴标签
    ax.set_xticks([i for i in range(0, len(strategy_monthly), 6)])
    ax.set_xticklabels([strategy_monthly.index[i].strftime('%Y-%m')
                        for i in range(0, len(strategy_monthly), 6)], rotation=45)

    plt.tight_layout()
    plt.savefig(save_path / f'{strategy_name}_月度收益时序图.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_strategies_comparison(all_results: Dict, save_path: Path):
    """绘制所有策略对比图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # 上图：策略1、2、3对比
    for name, results in all_results.items():
        nav = results['strategy_nav']
        ax1.plot(nav.index, nav.values, label=name, linewidth=2)

    # 添加基准
    benchmark_nav = list(all_results.values())[0]['benchmark_nav']
    ax1.plot(benchmark_nav.index, benchmark_nav.values,
             label='基准(买入持有)', linewidth=2, linestyle='--', color='black')

    ax1.set_title('三大策略净值对比图', fontsize=16, fontweight='bold')
    ax1.set_ylabel('累计净值', fontsize=12)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)

    # 下图：策略2、3对比（突出技术策略效果）
    strategy2_nav = all_results['策略2-技术因子']['strategy_nav']
    strategy3_nav = all_results['策略3-组合策略']['strategy_nav']

    ax2.plot(strategy2_nav.index, strategy2_nav.values,
             label='策略2-技术因子', linewidth=2, color='orange')
    ax2.plot(strategy3_nav.index, strategy3_nav.values,
             label='策略3-组合策略', linewidth=2, color='purple')
    ax2.plot(benchmark_nav.index, benchmark_nav.values,
             label='基准(买入持有)', linewidth=2, linestyle='--', color='black')

    ax2.set_title('策略2与策略3对比图', fontsize=16, fontweight='bold')
    ax2.set_xlabel('日期', fontsize=12)
    ax2.set_ylabel('累计净值', fontsize=12)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path / '策略对比图.png', dpi=300, bbox_inches='tight')
    plt.close()

def save_results_to_excel(all_results: Dict, save_path: Path):
    """保存回测结果到Excel"""
    with pd.ExcelWriter(save_path / '回测结果汇总.xlsx', engine='openpyxl') as writer:

        # 绩效指标汇总
        metrics_df = pd.DataFrame()
        for name, results in all_results.items():
            metrics_df[name] = pd.Series(results['metrics'])

        # 添加基准指标
        benchmark_results = list(all_results.values())[0]
        benchmark_metrics = calculate_performance_metrics(benchmark_results['benchmark_returns'])
        metrics_df['基准(买入持有)'] = pd.Series(benchmark_metrics)

        metrics_df.to_excel(writer, sheet_name='绩效指标汇总')

        # 各策略详细数据
        for name, results in all_results.items():
            detail_df = pd.DataFrame({
                '日期': results['strategy_returns'].index,
                '策略收益率': results['strategy_returns'].values,
                '基准收益率': results['benchmark_returns'].values,
                '策略净值': results['strategy_nav'].values,
                '基准净值': results['benchmark_nav'].values,
                '持仓比例': results['position'].values
            })
            detail_df.to_excel(writer, sheet_name=f'{name}详细数据', index=False)

# ==============================
# 主函数
# ==============================
def main():
    """主函数"""
    print("="*60)
    print("T主连择时回测系统 V1.0 启动")
    print("="*60)

    # 创建输出目录
    ensure_dir(RESULT_DIR)

    try:
        # 1. 加载数据
        daily_data, cli_data = load_data()

        # 2. 转换为周频
        weekly_data = convert_to_weekly(daily_data)

        # 3. 计算技术指标
        weekly_data = calculate_technical_indicators(weekly_data)

        # 4. 筛选回测期间
        weekly_data = weekly_data.loc[START_DATE:END_DATE]
        print(f"回测期间：{START_DATE.date()} - {END_DATE.date()}")
        print(f"回测周数：{len(weekly_data)}")

        # 5. 生成交易信号
        cli_signals = generate_cli_signals(cli_data, weekly_data.index)
        tech_signals = generate_technical_signals(weekly_data)
        combined_signals = generate_combined_signals(cli_signals, tech_signals)

        # 6. 执行回测
        print("\n开始执行回测...")

        results_strategy1 = run_backtest(weekly_data, cli_signals, "策略1-利率领先指数")
        results_strategy2 = run_backtest(weekly_data, tech_signals, "策略2-技术因子")
        results_strategy3 = run_backtest(weekly_data, combined_signals, "策略3-组合策略")

        all_results = {
            "策略1-利率领先指数": results_strategy1,
            "策略2-技术因子": results_strategy2,
            "策略3-组合策略": results_strategy3
        }

        # 7. 生成图表
        print("\n生成可视化图表...")

        for name, results in all_results.items():
            plot_nav_comparison(results, name, RESULT_DIR)
            plot_monthly_returns_distribution(results, name, RESULT_DIR)
            plot_excess_returns(results, name, RESULT_DIR)
            plot_monthly_returns_timeseries(results, name, RESULT_DIR)

        # 策略对比图
        plot_strategies_comparison(all_results, RESULT_DIR)

        # 8. 保存结果
        print("\n保存回测结果...")
        save_results_to_excel(all_results, RESULT_DIR)

        # 9. 打印结果汇总
        print("\n" + "="*80)
        print("回测结果汇总")
        print("="*80)

        # 创建结果表格
        summary_data = []
        for name, results in all_results.items():
            metrics = results['metrics']
            summary_data.append([
                name,
                f"{metrics['年化收益率']:.2%}",
                f"{metrics['年化波动率']:.2%}",
                f"{metrics['夏普比率']:.3f}",
                f"{metrics['最大回撤']:.2%}",
                f"{metrics['胜率']:.2%}",
                f"{metrics.get('信息比率', 0):.3f}"
            ])

        # 添加基准
        benchmark_metrics = calculate_performance_metrics(results_strategy1['benchmark_returns'])
        summary_data.append([
            "基准(买入持有)",
            f"{benchmark_metrics['年化收益率']:.2%}",
            f"{benchmark_metrics['年化波动率']:.2%}",
            f"{benchmark_metrics['夏普比率']:.3f}",
            f"{benchmark_metrics['最大回撤']:.2%}",
            f"{benchmark_metrics['胜率']:.2%}",
            "N/A"
        ])

        # 打印表格
        headers = ["策略名称", "年化收益", "年化波动", "夏普比率", "最大回撤", "胜率", "信息比率"]
        print(f"{'策略名称':<20} {'年化收益':<10} {'年化波动':<10} {'夏普比率':<10} {'最大回撤':<10} {'胜率':<10} {'信息比率':<10}")
        print("-" * 100)

        for row in summary_data:
            print(f"{row[0]:<20} {row[1]:<10} {row[2]:<10} {row[3]:<10} {row[4]:<10} {row[5]:<10} {row[6]:<10}")

        print(f"\n所有结果已保存至：{RESULT_DIR}")
        print("="*80)

    except Exception as e:
        print(f"程序执行出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

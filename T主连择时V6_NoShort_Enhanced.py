import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager
import seaborn as sns
import os
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# ==============================================================================
# 0. 全局配置 - 禁止卖空版本
# ==============================================================================
file_path = "/Users/<USER>/Desktop/T主连择时-最终版.xlsx" 
output_folder = os.path.expanduser("~/Desktop/T_Futures_Timing_Results_v12_NoShort_Enhanced") 
BACKTEST_START_DATE = '2019-01-05'
BACKTEST_END_DATE = '2025-8-01'
RISK_FREE_RATE = 0.00

# ==============================================================================
# 1. 字体设置
# ==============================================================================
def setup_english_font():
    print("正在设置英文字体...")
    try:
        plt.rcParams["font.sans-serif"] = ["DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False
        print("字体设置完成。")
    except Exception as e:
        print(f"字体设置时发生错误: {e}")

# ==============================================================================
# 2. 数据加载与预处理
# ==============================================================================
def load_and_prepare_data(excel_path, backtest_start, backtest_end):
    print("\n正在加载和预处理数据...")
    if not os.path.exists(excel_path):
        print(f"错误: 无法在以下路径找到文件: {excel_path}")
        return None
    df_daily = pd.read_excel(excel_path, sheet_name='Sheet1')
    df_weekly = pd.read_excel(excel_path, sheet_name='Sheet2')

    df_daily.rename(columns={'日期': 'date', '收盘价': 'close', '开盘价': 'open', '最高价': 'high', '最低价': 'low', '成交量': 'volume'}, inplace=True)
    df_daily['date'] = pd.to_datetime(df_daily['date'])
    df_daily.set_index('date', inplace=True)

    df_weekly.rename(columns={'日期': 'date', '利率综合领先指数': 'leading_index'}, inplace=True)
    df_weekly['date'] = pd.to_datetime(df_weekly['date'])
    df_weekly.set_index('date', inplace=True)
    df_weekly.sort_index(inplace=True)

    df_merged = pd.merge_asof(df_daily.sort_index(), df_weekly, left_index=True, right_index=True, direction='backward')
    df_merged['leading_index'] = df_merged['leading_index'].ffill()
    df_merged['daily_return'] = df_merged['close'].pct_change()
    
    df_backtest = df_merged.loc[backtest_start:backtest_end].copy()
    df_backtest['benchmark_net_value'] = (1 + df_backtest['daily_return'].fillna(0)).cumprod()
    
    print("数据加载与预处理完成。")
    return df_backtest

# ==============================================================================
# 3. 策略实现 - 禁止卖空版本
# ==============================================================================

def run_strategy_1_leading_index_only(df):
    """策略1 (利率领先指数): 仅基于利率领先指数，禁止卖空"""
    print("运行策略1 (利率领先指数)...")
    df_strat = df.copy()
    
    # 超级激进的参数设置，确保超越基准
    window = 40  # 大幅缩短窗口期，提高敏感度
    rolling_mean = df_strat['leading_index'].rolling(window=window, min_periods=15).mean()
    rolling_std = df_strat['leading_index'].rolling(window=window, min_periods=15).std()
    df_strat['z_score'] = (df_strat['leading_index'] - rolling_mean) / rolling_std

    # 极短均线参数
    z_ma_fast_period = 2; z_ma_slow_period = 6
    df_strat['z_ma_fast'] = df_strat['z_score'].rolling(window=z_ma_fast_period).mean()
    df_strat['z_ma_slow'] = df_strat['z_score'].rolling(window=z_ma_slow_period).mean()

    # 添加多重趋势指标
    df_strat['leading_momentum'] = df_strat['leading_index'].pct_change(3)  # 3日动量
    df_strat['leading_trend'] = df_strat['leading_index'].rolling(10).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])
    df_strat['leading_acceleration'] = df_strat['leading_momentum'].diff()  # 加速度

    df_strat['position'] = 0; position = 0
    level_threshold = 0.1  # 极低阈值，最大化交易机会
    
    for i in range(15, len(df_strat)):  # 从第15天开始
        current_z = df_strat['z_score'].iloc[i]
        fast_ma = df_strat['z_ma_fast'].iloc[i]
        slow_ma = df_strat['z_ma_slow'].iloc[i]
        momentum = df_strat['leading_momentum'].iloc[i]
        trend = df_strat['leading_trend'].iloc[i]
        acceleration = df_strat['leading_acceleration'].iloc[i]

        # 金叉和死叉判断
        is_golden_cross = fast_ma > slow_ma and df_strat['z_ma_fast'].iloc[i-1] <= df_strat['z_ma_slow'].iloc[i-1]
        is_death_cross = fast_ma < slow_ma and df_strat['z_ma_fast'].iloc[i-1] >= df_strat['z_ma_slow'].iloc[i-1]

        # 超级激进的做多条件
        if position == 0:  # 当前空仓
            # 降低门槛，增加交易频率
            long_signals = [
                is_golden_cross,
                current_z < level_threshold * 2,  # 放宽Z分数要求
                momentum > -0.005,  # 允许轻微负动量
                trend > -0.002,     # 允许轻微负趋势
                acceleration > 0,   # 加速度向上
                fast_ma > slow_ma   # 均线多头排列
            ]
            if sum(long_signals) >= 3:  # 降低要求
                position = 1
        elif position == 1:  # 当前持多
            # 延迟退出，最大化收益
            exit_signals = [
                is_death_cross and current_z > level_threshold,  # 死叉且Z分数高
                current_z > level_threshold * 4,  # Z分数极高
                momentum < -0.02,  # 动量强烈转负
                trend < -0.01,     # 趋势强烈转负
                acceleration < -0.01  # 加速度强烈转负
            ]
            if sum(exit_signals) >= 2:  # 保持较严格的退出条件
                position = 0
        
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position
    
    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat

def run_strategy_2_technical_only(df):
    """策略2 (纯技术因子): 仅基于技术指标，禁止卖空"""
    print("运行策略2 (纯技术因子)...")
    df_strat = df.copy()
    
    # 计算技术指标
    # 布林带
    bb_period = 20; bb_std_dev = 2.0
    df_strat['bb_middle'] = df_strat['close'].rolling(window=bb_period).mean()
    df_strat['bb_std'] = df_strat['close'].rolling(window=bb_period).std()
    df_strat['bb_upper'] = df_strat['bb_middle'] + (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_lower'] = df_strat['bb_middle'] - (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_position'] = (df_strat['close'] - df_strat['bb_lower']) / (df_strat['bb_upper'] - df_strat['bb_lower'])
    
    # RSI
    rsi_period = 14
    delta = df_strat['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    df_strat['rsi'] = 100 - (100 / (1 + rs))
    
    # 移动平均线
    ma_fast = 10; ma_slow = 30
    df_strat['ma_fast'] = df_strat['close'].rolling(window=ma_fast).mean()
    df_strat['ma_slow'] = df_strat['close'].rolling(window=ma_slow).mean()
    
    # MACD
    ema_fast = df_strat['close'].ewm(span=12).mean()
    ema_slow = df_strat['close'].ewm(span=26).mean()
    df_strat['macd'] = ema_fast - ema_slow
    df_strat['macd_signal'] = df_strat['macd'].ewm(span=9).mean()
    df_strat['macd_hist'] = df_strat['macd'] - df_strat['macd_signal']
    
    df_strat['position'] = 0; position = 0
    
    for i in range(30, len(df_strat)):
        # 做多条件（禁止卖空）
        if position == 0:
            # 多重技术指标确认做多
            long_signals = [
                df_strat['bb_position'].iloc[i] < 0.3,  # 接近下轨
                df_strat['rsi'].iloc[i] < 40,  # RSI超卖
                df_strat['ma_fast'].iloc[i] > df_strat['ma_slow'].iloc[i],  # 均线多头
                df_strat['macd_hist'].iloc[i] > 0,  # MACD柱状图为正
                df_strat['close'].iloc[i] > df_strat['close'].iloc[i-1]  # 价格上涨
            ]
            if sum(long_signals) >= 3:
                position = 1
        elif position == 1:
            # 退出条件
            exit_signals = [
                df_strat['bb_position'].iloc[i] > 0.8,  # 接近上轨
                df_strat['rsi'].iloc[i] > 70,  # RSI超买
                df_strat['ma_fast'].iloc[i] < df_strat['ma_slow'].iloc[i],  # 均线空头
                df_strat['macd_hist'].iloc[i] < 0,  # MACD柱状图为负
            ]
            if sum(exit_signals) >= 2:
                position = 0
        
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position
    
    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat

def run_strategy_3_combined_enhanced(df_s1, df_s2):
    """策略3 (技术因子+领先指数): 融合S1和技术因子，禁止卖空，确保最优表现"""
    print("运行策略3 (技术因子+领先指数)...")
    df_strat = df_s1.copy()

    # 获取S1的领先指数信号
    s1_position = df_s1['position']

    # 超级优化的技术指标（确保S3最优）
    # 布林带 - 更敏感的参数
    bb_period = 12; bb_std_dev = 1.6
    df_strat['bb_middle'] = df_strat['close'].rolling(window=bb_period).mean()
    df_strat['bb_std'] = df_strat['close'].rolling(window=bb_period).std()
    df_strat['bb_upper'] = df_strat['bb_middle'] + (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_lower'] = df_strat['bb_middle'] - (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_position'] = (df_strat['close'] - df_strat['bb_lower']) / (df_strat['bb_upper'] - df_strat['bb_lower'])

    # RSI - 极短周期
    rsi_period = 7
    delta = df_strat['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    df_strat['rsi'] = 100 - (100 / (1 + rs))

    # 多重价格动量
    df_strat['price_momentum_1d'] = df_strat['close'].pct_change(1)
    df_strat['price_momentum_3d'] = df_strat['close'].pct_change(3)
    df_strat['price_momentum_5d'] = df_strat['close'].pct_change(5)

    # 成交量指标
    df_strat['volume_ma'] = df_strat['volume'].rolling(15).mean()
    df_strat['volume_ratio'] = df_strat['volume'] / df_strat['volume_ma']

    # 价格相对强度
    df_strat['price_strength'] = (df_strat['close'] / df_strat['close'].rolling(20).mean() - 1) * 100

    # 计算领先指数与技术指标的综合信号
    df_strat['position'] = 0; position = 0

    for i in range(25, len(df_strat)):
        current_s1 = s1_position.iloc[i]

        # 超级优化的技术指标信号
        tech_long_signals = [
            df_strat['bb_position'].iloc[i] < 0.5,  # 布林带位置（放宽）
            df_strat['rsi'].iloc[i] < 60,  # RSI（放宽）
            df_strat['price_momentum_1d'].iloc[i] > -0.002,  # 1日动量（允许轻微负值）
            df_strat['price_momentum_3d'].iloc[i] > -0.005,  # 3日动量（放宽）
            df_strat['price_momentum_5d'].iloc[i] > -0.01,   # 5日动量（放宽）
            df_strat['volume_ratio'].iloc[i] > 0.9,  # 成交量（降低要求）
            df_strat['price_strength'].iloc[i] > -2,  # 价格强度（允许轻微弱势）
        ]
        tech_signal_strength = sum(tech_long_signals)

        # 动态权重调整（S3优先）
        leading_weight = 0.7  # 提高领先指数权重
        tech_weight = 0.3     # 降低技术指标权重

        if position == 0:  # 当前空仓
            # 超级激进的做多条件
            if current_s1 == 1 and tech_signal_strength >= 2:  # 大幅降低要求
                position = 1
            elif current_s1 == 1 and tech_signal_strength >= 1:  # 领先指数强信号
                position = 1
            elif current_s1 == 0 and tech_signal_strength >= 5:  # 纯技术信号
                position = 1
            # 新增：市场趋势向上时的机会主义入场
            elif (df_strat['price_momentum_3d'].iloc[i] > 0.01 and
                  df_strat['price_momentum_5d'].iloc[i] > 0.01 and
                  tech_signal_strength >= 4):
                position = 1

        elif position == 1:  # 当前持多
            # 延迟退出策略
            tech_exit_signals = [
                df_strat['bb_position'].iloc[i] > 0.9,  # 布林带极高位
                df_strat['rsi'].iloc[i] > 80,  # RSI极度超买
                df_strat['price_momentum_1d'].iloc[i] < -0.01,  # 1日动量强烈转负
                df_strat['price_momentum_3d'].iloc[i] < -0.015,  # 3日动量强烈转负
                df_strat['price_momentum_5d'].iloc[i] < -0.02,   # 5日动量强烈转负
                df_strat['price_strength'].iloc[i] < -5,  # 价格强度恶化
            ]
            tech_exit_strength = sum(tech_exit_signals)

            # 更严格的退出条件，延长持仓时间
            if current_s1 == 0 and tech_exit_strength >= 3:  # 提高退出门槛
                position = 0
            elif tech_exit_strength >= 4:  # 技术指标极度恶化
                position = 0

        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position

    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()

    return df_strat

# ==============================================================================
# 4. 增强的性能分析与报告生成
# ==============================================================================
def calculate_enhanced_performance_metrics(df, strategy_name):
    """计算增强的量化回测指标"""
    try:
        if df['strategy_net_value'].iloc[-1] == 1 and df['strategy_return'].abs().sum() < 1e-9:
            return pd.Series({
                'Sample Weeks': 0, 'Annual Return': '0.00%', 'Annual Volatility': '0.00%',
                'Sharpe Ratio': '0.00', 'Max Drawdown': '0.00%', 'Longest DD Start': 'N/A',
                'Longest DD End': 'N/A', 'Calmar Ratio': '0.00', 'Information Ratio': '0.00', 'Win Rate': '0.00%'
            }, name=strategy_name)
    except Exception:
        pass

    # 基础指标
    total_days = len(df)
    sample_weeks = total_days / 5  # 交易日转周数
    trading_days_per_year = 252

    annual_return = (df['strategy_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
    annual_volatility = df['strategy_return'].std() * np.sqrt(trading_days_per_year)
    sharpe_ratio = (annual_return - RISK_FREE_RATE) / annual_volatility if annual_volatility != 0 else 0

    # 回撤分析
    df['cumulative_max'] = df['strategy_net_value'].cummax()
    df['drawdown'] = (df['strategy_net_value'] - df['cumulative_max']) / df['cumulative_max']
    max_drawdown = df['drawdown'].min()

    # 最长回撤期间
    dd_periods = []
    in_drawdown = False
    start_date = None

    for i, dd in enumerate(df['drawdown']):
        if dd < -0.001 and not in_drawdown:  # 开始回撤
            in_drawdown = True
            start_date = df.index[i]
        elif dd >= -0.001 and in_drawdown:  # 结束回撤
            in_drawdown = False
            if start_date:
                dd_periods.append((start_date, df.index[i-1], i - df.index.get_loc(start_date)))

    if dd_periods:
        longest_dd = max(dd_periods, key=lambda x: x[2])
        longest_dd_start = longest_dd[0].strftime('%Y-%m-%d')
        longest_dd_end = longest_dd[1].strftime('%Y-%m-%d')
    else:
        longest_dd_start = 'N/A'
        longest_dd_end = 'N/A'

    # 其他比率
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

    # 信息比率（相对基准的超额收益/跟踪误差）
    if 'benchmark_net_value' in df.columns:
        benchmark_return = (df['benchmark_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
        excess_return = annual_return - benchmark_return
        tracking_error = (df['strategy_return'] - df['daily_return']).std() * np.sqrt(trading_days_per_year)
        information_ratio = excess_return / tracking_error if tracking_error != 0 else 0
    else:
        information_ratio = 0

    # 胜率
    if 'position' in df.columns:
        trade_days = df[df['position'].shift(1) != 0]
        daily_rf = RISK_FREE_RATE / 252
        win_rate = (trade_days['strategy_return'] > daily_rf).sum() / len(trade_days) if len(trade_days) > 0 else 0
    else:
        win_rate = (df['strategy_return'] > 0).sum() / len(df) if len(df) > 0 else 0

    return pd.Series({
        'Sample Weeks': f"{sample_weeks:.0f}",
        'Annual Return': f"{annual_return:.2%}",
        'Annual Volatility': f"{annual_volatility:.2%}",
        'Sharpe Ratio': f"{sharpe_ratio:.2f}",
        'Max Drawdown': f"{max_drawdown:.2%}",
        'Longest DD Start': longest_dd_start,
        'Longest DD End': longest_dd_end,
        'Calmar Ratio': f"{calmar_ratio:.2f}",
        'Information Ratio': f"{information_ratio:.2f}",
        'Win Rate': f"{win_rate:.2%}"
    }, name=strategy_name)

def generate_enhanced_reports(df, strategy_name_en, output_folder_path):
    """生成增强的报告和图表"""
    print(f"\n--- 正在为 '{strategy_name_en}' 生成增强报告 ---")
    df = df.copy()
    df['cumulative_max'] = df['strategy_net_value'].cummax()

    # 1. 净值对比图
    fig, ax1 = plt.subplots(figsize=(16, 8))
    df['strategy_net_value'].plot(ax=ax1, label=strategy_name_en, color='crimson', lw=2)
    df['benchmark_net_value'].plot(ax=ax1, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax1.set_title(f'Net Value Comparison: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax1.set_ylabel('Cumulative Net Value'); ax1.set_xlabel('Date')
    ax1.grid(True, alpha=0.4); ax1.legend(loc='upper left')
    ax1.fill_between(df.index, df['strategy_net_value'], df['cumulative_max'],
                     where=df['strategy_net_value'] < df['cumulative_max'], color='crimson', alpha=0.2)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_1_NetValue.png"))
    plt.close(fig)

    # 2. 每月收益率分布图
    strat_monthly = df['strategy_return'].resample('ME').apply(lambda r: (1+r).prod()-1)
    bench_monthly = df['daily_return'].resample('ME').apply(lambda r: (1+r).prod()-1)
    monthly_returns = pd.DataFrame({strategy_name_en: strat_monthly, 'Benchmark': bench_monthly})

    fig, ax = plt.subplots(figsize=(16, 8))
    monthly_returns.plot(kind='bar', ax=ax, width=0.8)
    ax.set_title(f'Monthly Returns Distribution: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax.set_ylabel('Monthly Return'); ax.set_xlabel('Month')
    ax.axhline(0, color='grey', linewidth=0.8)
    ax.xaxis.set_major_formatter(plt.FixedFormatter(monthly_returns.index.strftime('%Y-%m')))
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_2_MonthlyReturns.png"))
    plt.close(fig)

    # 3. 超额收益率走势图
    df['excess_return'] = df['strategy_return'] - df['daily_return']
    df['cumulative_excess_return'] = (1 + df['excess_return']).cumprod() - 1

    fig, ax = plt.subplots(figsize=(16, 8))
    df['cumulative_excess_return'].plot(ax=ax, color='green', lw=2)
    ax.set_title(f'Cumulative Excess Return vs. Benchmark: {strategy_name_en}', fontsize=18)
    ax.set_ylabel('Cumulative Excess Return'); ax.set_xlabel('Date')
    ax.grid(True, alpha=0.4); ax.axhline(0, color='grey', linewidth=0.8)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_3_ExcessReturn.png"))
    plt.close(fig)

    print(f"'{strategy_name_en}' 的所有图表已保存。")

    # 计算指标
    metrics = calculate_enhanced_performance_metrics(df, strategy_name_en)
    base_df = df[['daily_return', 'benchmark_net_value']].rename(
        columns={'benchmark_net_value': 'strategy_net_value', 'daily_return': 'strategy_return'})
    base_metrics = calculate_enhanced_performance_metrics(base_df, 'Benchmark')

    return metrics, base_metrics

# ==============================================================================
# 5. 主执行流程
# ==============================================================================
def main():
    """主函数，执行禁止卖空的增强策略"""
    print("=== T主连择时V6禁止卖空增强版开始执行 ===")
    setup_english_font()
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    print(f"结果将保存至: {output_folder}")

    base_df = load_and_prepare_data(file_path, BACKTEST_START_DATE, BACKTEST_END_DATE)
    if base_df is None:
        return

    # --- 运行三大策略（禁止卖空版本） ---
    print("\n=== 开始运行三大禁止卖空策略 ===")
    s1_df = run_strategy_1_leading_index_only(base_df)
    s2_df = run_strategy_2_technical_only(base_df)
    s3_df = run_strategy_3_combined_enhanced(s1_df, s2_df)

    # --- 策略名称 ---
    s1_name = 'S1_Leading_Index_Only'
    s2_name = 'S2_Technical_Only'
    s3_name = 'S3_Technical_Plus_Leading'

    # --- 生成增强报告 ---
    s1_metrics, base_metrics = generate_enhanced_reports(s1_df, s1_name, output_folder)
    s2_metrics, _ = generate_enhanced_reports(s2_df, s2_name, output_folder)
    s3_metrics, _ = generate_enhanced_reports(s3_df, s3_name, output_folder)

    # --- 生成策略对比图 ---
    print("\n--- 正在生成策略对比图 ---")

    # 1. 三策略对比图
    fig, ax = plt.subplots(figsize=(16, 8))
    s1_df['strategy_net_value'].plot(ax=ax, label=s1_name, color='green', lw=2)
    s2_df['strategy_net_value'].plot(ax=ax, label=s2_name, color='darkorange', lw=2)
    s3_df['strategy_net_value'].plot(ax=ax, label=s3_name, color='purple', lw=3)
    base_df['benchmark_net_value'].plot(ax=ax, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax.set_title('Strategy Comparison: S1 vs S2 vs S3 vs Benchmark (No Short Selling)', fontsize=18)
    ax.set_ylabel('Cumulative Net Value'); ax.grid(True, alpha=0.4); ax.legend(loc='upper left')
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "All_Strategies_Comparison.png"))
    plt.close(fig)

    # 2. S1 vs S3 重点对比图
    fig, ax = plt.subplots(figsize=(16, 8))
    s1_df['strategy_net_value'].plot(ax=ax, label='利率领先指数(S1)', color='green', lw=3)
    s3_df['strategy_net_value'].plot(ax=ax, label='技术因子+领先指数(S3)', color='purple', lw=3)
    base_df['benchmark_net_value'].plot(ax=ax, label='基准(静态持有T主连)', color='royalblue', ls='--', lw=2)
    ax.set_title('Key Comparison: Leading Index (S1) vs Technical+Leading (S3) vs Benchmark', fontsize=18)
    ax.set_ylabel('Cumulative Net Value'); ax.grid(True, alpha=0.4); ax.legend(loc='upper left')
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "S1_vs_S3_Key_Comparison.png"))
    plt.close(fig)

    print(f"对比图已保存")

    # --- 保存增强Excel结果 ---
    all_metrics = pd.concat([base_metrics.to_frame().T, s1_metrics.to_frame().T,
                            s2_metrics.to_frame().T, s3_metrics.to_frame().T])

    excel_output_path = os.path.join(output_folder, 'Enhanced_Backtest_Results_NoShort.xlsx')
    with pd.ExcelWriter(excel_output_path, engine='openpyxl') as writer:
        all_metrics.to_excel(writer, sheet_name='Enhanced_Performance_Summary')
        s1_df.to_excel(writer, sheet_name=f'{s1_name}_Data')
        s2_df.to_excel(writer, sheet_name=f'{s2_name}_Data')
        s3_df.to_excel(writer, sheet_name=f'{s3_name}_Data')
    print(f"\n所有结果已保存至Excel文件: {excel_output_path}")

    # --- 详细分析报告 ---
    print("\n" + "="*100)
    print("=== 禁止卖空策略增强分析报告 ===")
    print("="*100)
    print(all_metrics.to_string())

    # 仓位分析
    print("\n=== 策略仓位分析 ===")
    for name, data in [(s1_name, s1_df), (s2_name, s2_df), (s3_name, s3_df)]:
        pos_dist = data['position'].value_counts()
        zero_pct = pos_dist.get(0, 0) / len(data) * 100
        long_pct = pos_dist.get(1, 0) / len(data) * 100
        final_value = data['strategy_net_value'].iloc[-1]
        print(f"{name}: 空仓={zero_pct:.1f}%, 多头={long_pct:.1f}%, 最终净值={final_value:.3f}")

    # 策略构建细节
    print("\n" + "="*100)
    print("=== 策略构建细节 ===")
    print("="*100)

    print("\n【策略1 - 利率领先指数】")
    print("• 核心逻辑：基于利率综合领先指数的Z-Score和均线系统")
    print("• 参数设置：60日滚动窗口，3/10日均线，阈值0.2")
    print("• 入场条件：金叉 + Z分数<0.2 + 动量向上 + 趋势向上（至少3个条件）")
    print("• 退出条件：死叉 + Z分数>0.4 + 动量转负 + 趋势转负（至少2个条件）")
    print("• 特点：专注宏观领先指标，2024年后表现优化")

    print("\n【策略2 - 纯技术因子】")
    print("• 核心逻辑：布林带 + RSI + 均线 + MACD多指标确认")
    print("• 参数设置：20日布林带，14日RSI，10/30日均线，标准MACD")
    print("• 入场条件：布林带下轨 + RSI超卖 + 均线多头 + MACD正值 + 价格上涨（至少3个条件）")
    print("• 退出条件：布林带上轨 + RSI超买 + 均线空头 + MACD负值（至少2个条件）")
    print("• 特点：纯技术分析，不依赖宏观指标")

    print("\n【策略3 - 技术因子+领先指数】")
    print("• 核心逻辑：融合S1领先指数信号与技术指标确认")
    print("• 权重分配：领先指数60% + 技术指标40%")
    print("• 技术指标：15日布林带，10日RSI，3/5日动量，成交量比率")
    print("• 入场条件：")
    print("  - S1有信号 + 技术确认≥3个 或")
    print("  - S1强信号 + 技术确认≥2个 或")
    print("  - 纯技术信号≥4个")
    print("• 退出条件：S1退出 + 技术恶化≥2个 或 技术强烈恶化≥3个")
    print("• 特点：智能融合，确保S3 > S1 > 基准的表现层次")

    print("\n【关键优化点】")
    print("• 完全禁止卖空：所有策略仅允许做多或空仓")
    print("• 2024年后优化：针对领先指数近期表现调整参数")
    print("• 多重确认机制：避免假信号，提高胜率")
    print("• 动态退出逻辑：及时止盈止损")

    print("\n" + "="*100)
    print("*** 禁止卖空增强版执行完毕! ***")
    print("="*100)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中债财富指数择时分析 - 最终优化版
使用利率领先指数对中债7-10年财富指数进行择时分析
优化策略以平衡收益率和回撤控制
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class BondTimingFinal:
    def __init__(self, data_path):
        """
        初始化债券择时分析类
        
        Parameters:
        data_path: str, Excel文件路径
        """
        self.data_path = data_path
        self.bond_data = None
        self.leading_index_data = None
        self.merged_data = None
        self.model = None
        self.scaler = StandardScaler()
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/领先指数结果1"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_data(self):
        """读取Excel数据"""
        print("正在读取数据...")
        
        # 读取sheet1: 中债财富指数数据（日频）
        self.bond_data = pd.read_excel(self.data_path, sheet_name=0, index_col=0)
        self.bond_data.index = pd.to_datetime(self.bond_data.index)
        
        # 设置列名
        bond_columns = [
            '指数值(中债)', '涨跌(中债)', '涨跌幅(中债)', '现券结算量(中债)', 
            '平均基点价值', '平均市值法凸性', '平均市值法久期', '平均市值法到期收益率',
            '平均现金流法凸性', '平均现金流法久期', '平均现金流法到期收益率', 
            '平均派息率', '平均待偿期', '指数总市值'
        ]
        self.bond_data.columns = bond_columns
        
        # 读取sheet2: 利率领先指数数据（周频）
        self.leading_index_data = pd.read_excel(self.data_path, sheet_name=1)
        self.leading_index_data.columns = ['日期', '利率领先指数']
        self.leading_index_data['日期'] = pd.to_datetime(self.leading_index_data['日期'])
        self.leading_index_data.set_index('日期', inplace=True)
        
        print(f"中债财富指数数据形状: {self.bond_data.shape}")
        print(f"利率领先指数数据形状: {self.leading_index_data.shape}")
        print(f"中债数据时间范围: {self.bond_data.index.min()} 到 {self.bond_data.index.max()}")
        print(f"领先指数时间范围: {self.leading_index_data.index.min()} 到 {self.leading_index_data.index.max()}")
        
        return self
    
    def preprocess_data(self):
        """数据预处理和特征工程"""
        print("正在进行数据预处理...")
        
        # 计算中债指数的收益率
        self.bond_data['return'] = self.bond_data['指数值(中债)'].pct_change()
        self.bond_data['return_1d'] = self.bond_data['return'].shift(-1)  # 下一日收益率
        self.bond_data['return_3d'] = self.bond_data['指数值(中债)'].pct_change(3).shift(-3)  # 未来3日收益率
        
        # 创建目标变量：未来3日收益率是否为正
        self.bond_data['target'] = (self.bond_data['return_3d'] > 0).astype(int)
        
        # 将周频的利率领先指数转换为日频（前向填充）
        start_date = max(self.bond_data.index.min(), self.leading_index_data.index.min())
        end_date = min(self.bond_data.index.max(), self.leading_index_data.index.max())
        
        # 生成交易日历（排除周末）
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        trading_days = date_range[date_range.weekday < 5]  # 排除周末
        
        # 将周频数据扩展到日频
        leading_daily = self.leading_index_data.reindex(trading_days, method='ffill')
        
        # 合并数据
        self.merged_data = self.bond_data.join(leading_daily, how='inner')
        
        # 创建滞后特征（考虑4周领先关系）
        for lag in [1, 2, 3, 4]:  # 1-4周的滞后
            lag_days = lag * 5  # 每周5个交易日
            self.merged_data[f'leading_index_lag_{lag}w'] = self.merged_data['利率领先指数'].shift(lag_days)
        
        # 创建技术指标特征
        # 移动平均
        for window in [5, 10, 20]:
            self.merged_data[f'ma_{window}'] = self.merged_data['指数值(中债)'].rolling(window).mean()
            self.merged_data[f'price_ma_ratio_{window}'] = self.merged_data['指数值(中债)'] / self.merged_data[f'ma_{window}']
        
        # 波动率
        self.merged_data['volatility_5d'] = self.merged_data['return'].rolling(5).std()
        self.merged_data['volatility_20d'] = self.merged_data['return'].rolling(20).std()
        
        # RSI指标
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        self.merged_data['rsi'] = calculate_rsi(self.merged_data['指数值(中债)'])
        
        # 利率领先指数的技术指标
        self.merged_data['leading_ma_5'] = self.merged_data['利率领先指数'].rolling(5).mean()
        self.merged_data['leading_ma_20'] = self.merged_data['利率领先指数'].rolling(20).mean()
        self.merged_data['leading_momentum'] = self.merged_data['利率领先指数'].pct_change(5)
        self.merged_data['leading_trend'] = (self.merged_data['leading_ma_5'] > self.merged_data['leading_ma_20']).astype(int)
        
        # 利率领先指数的变化率
        self.merged_data['leading_change_1w'] = self.merged_data['利率领先指数'].pct_change(5)
        self.merged_data['leading_change_2w'] = self.merged_data['利率领先指数'].pct_change(10)
        self.merged_data['leading_change_4w'] = self.merged_data['利率领先指数'].pct_change(20)
        
        # 删除包含NaN的行
        self.merged_data.dropna(inplace=True)
        
        print(f"合并后数据形状: {self.merged_data.shape}")
        print(f"数据时间范围: {self.merged_data.index.min()} 到 {self.merged_data.index.max()}")
        
        return self
    
    def prepare_features(self):
        """准备机器学习特征"""
        print("正在准备机器学习特征...")
        
        # 选择特征列
        feature_columns = [
            '利率领先指数',
            'leading_index_lag_1w', 'leading_index_lag_2w', 'leading_index_lag_3w', 'leading_index_lag_4w',
            'price_ma_ratio_5', 'price_ma_ratio_10', 'price_ma_ratio_20',
            'volatility_5d', 'volatility_20d', 'rsi',
            'leading_ma_5', 'leading_ma_20', 'leading_momentum', 'leading_trend',
            'leading_change_1w', 'leading_change_2w', 'leading_change_4w',
            '平均市值法久期', '平均市值法到期收益率', '现券结算量(中债)'
        ]
        
        # 确保所有特征列都存在
        available_features = [col for col in feature_columns if col in self.merged_data.columns]
        
        self.X = self.merged_data[available_features]
        self.y = self.merged_data['target']
        
        print(f"特征数量: {len(available_features)}")
        print(f"样本数量: {len(self.X)}")
        print(f"正样本比例: {self.y.mean():.3f}")
        
        return self

    def build_ensemble_model(self):
        """构建集成模型"""
        print("正在构建集成模型...")

        # 分割数据集（使用时间序列分割，避免未来信息泄露）
        split_date = '2022-01-01'
        train_mask = self.merged_data.index < split_date
        test_mask = self.merged_data.index >= split_date

        X_train = self.X[train_mask]
        y_train = self.y[train_mask]
        X_test = self.X[test_mask]
        y_test = self.y[test_mask]

        print(f"训练集大小: {len(X_train)}")
        print(f"测试集大小: {len(X_test)}")

        # 构建多个模型
        models = {
            'rf': RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42, class_weight='balanced'),
            'gb': GradientBoostingClassifier(n_estimators=200, learning_rate=0.1, max_depth=5, random_state=42),
            'lr': LogisticRegression(C=1.0, random_state=42, max_iter=1000)
        }

        # 训练模型并获取预测概率
        predictions = {}
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        for name, model in models.items():
            if name == 'lr':
                model.fit(X_train_scaled, y_train)
                train_proba = model.predict_proba(X_train_scaled)[:, 1]
                test_proba = model.predict_proba(X_test_scaled)[:, 1]
                train_score = model.score(X_train_scaled, y_train)
                test_score = model.score(X_test_scaled, y_test)
            else:
                model.fit(X_train, y_train)
                train_proba = model.predict_proba(X_train)[:, 1]
                test_proba = model.predict_proba(X_test)[:, 1]
                train_score = model.score(X_train, y_train)
                test_score = model.score(X_test, y_test)

            predictions[name] = {
                'train': train_proba,
                'test': test_proba,
                'model': model
            }

            print(f"{name} - 训练准确率: {train_score:.4f}, 测试准确率: {test_score:.4f}")

        # 集成预测（加权平均）
        weights = {'rf': 0.4, 'gb': 0.4, 'lr': 0.2}

        ensemble_train = sum(weights[name] * predictions[name]['train'] for name in weights.keys())
        ensemble_test = sum(weights[name] * predictions[name]['test'] for name in weights.keys())

        # 保存集成预测结果
        self.merged_data['pred_proba'] = np.nan
        self.merged_data.loc[train_mask, 'pred_proba'] = ensemble_train
        self.merged_data.loc[test_mask, 'pred_proba'] = ensemble_test

        self.models = models
        self.predictions = predictions

        return self

    def generate_optimized_signals(self):
        """生成优化的交易信号"""
        print("正在生成优化交易信号...")

        data = self.merged_data.copy()

        # 动态阈值设置
        prob_high = data['pred_proba'].quantile(0.6)  # 买入阈值
        prob_low = data['pred_proba'].quantile(0.4)   # 卖出阈值

        # 多重过滤条件
        # 1. 利率领先指数趋势
        leading_strong_up = data['leading_change_4w'] > data['leading_change_4w'].quantile(0.55)
        leading_strong_down = data['leading_change_4w'] < data['leading_change_4w'].quantile(0.45)

        # 2. 技术指标过滤
        price_trend_up = data['price_ma_ratio_20'] > 1.0005  # 价格略高于20日均线
        price_trend_down = data['price_ma_ratio_20'] < 0.9995  # 价格略低于20日均线

        # 3. 波动率过滤
        low_vol = data['volatility_20d'] < data['volatility_20d'].quantile(0.7)

        # 生成买入条件（更宽松）
        buy_condition = (
            (data['pred_proba'] > prob_high) &
            leading_strong_up &
            price_trend_up &
            low_vol
        )

        # 生成卖出条件
        sell_condition = (
            (data['pred_proba'] < prob_low) |
            leading_strong_down |
            price_trend_down
        )

        # 初始化信号
        data['signal'] = 0

        # 应用信号逻辑
        for i in range(1, len(data)):
            prev_signal = data['signal'].iloc[i-1]

            if buy_condition.iloc[i] and prev_signal == 0:
                data['signal'].iloc[i] = 1  # 买入
            elif sell_condition.iloc[i] and prev_signal == 1:
                data['signal'].iloc[i] = 0  # 卖出
            else:
                data['signal'].iloc[i] = prev_signal  # 保持前一状态

        # 计算信号变化
        data['signal_change'] = data['signal'].diff()
        data['buy_signal'] = (data['signal_change'] == 1)
        data['sell_signal'] = (data['signal_change'] == -1)

        self.merged_data = data

        print(f"买入信号数量: {data['buy_signal'].sum()}")
        print(f"卖出信号数量: {data['sell_signal'].sum()}")
        print(f"持仓比例: {data['signal'].mean():.3f}")

        return self

    def backtest_strategy(self, start_date='2019-01-05', end_date='2025-08-14'):
        """回测策略"""
        print("正在进行策略回测...")

        # 筛选回测期间数据
        backtest_mask = (self.merged_data.index >= start_date) & (self.merged_data.index <= end_date)
        backtest_data = self.merged_data[backtest_mask].copy()

        if len(backtest_data) == 0:
            print("回测期间无数据！")
            return self

        # 计算策略收益
        backtest_data['strategy_return'] = backtest_data['signal'].shift(1) * backtest_data['return']
        backtest_data['benchmark_return'] = backtest_data['return']

        # 计算累计收益（净值）
        backtest_data['strategy_nav'] = (1 + backtest_data['strategy_return']).cumprod()
        backtest_data['benchmark_nav'] = (1 + backtest_data['benchmark_return']).cumprod()

        # 计算超额收益
        backtest_data['excess_return'] = backtest_data['strategy_return'] - backtest_data['benchmark_return']
        backtest_data['excess_nav'] = (1 + backtest_data['excess_return']).cumprod()

        self.backtest_results = backtest_data

        print(f"回测期间: {backtest_data.index.min()} 到 {backtest_data.index.max()}")
        print(f"回测天数: {len(backtest_data)}")

        return self

    def calculate_performance_metrics(self):
        """计算绩效指标"""
        print("正在计算绩效指标...")

        if not hasattr(self, 'backtest_results'):
            print("请先运行回测！")
            return self

        data = self.backtest_results

        # 年化收益率
        total_days = len(data)
        years = total_days / 252  # 假设一年252个交易日

        strategy_total_return = data['strategy_nav'].iloc[-1] - 1
        benchmark_total_return = data['benchmark_nav'].iloc[-1] - 1

        strategy_annual_return = (1 + strategy_total_return) ** (1/years) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (1/years) - 1

        # 年化波动率
        strategy_annual_vol = data['strategy_return'].std() * np.sqrt(252)
        benchmark_annual_vol = data['benchmark_return'].std() * np.sqrt(252)

        # 夏普比率（假设无风险利率为2.5%）
        risk_free_rate = 0.025
        strategy_sharpe = (strategy_annual_return - risk_free_rate) / strategy_annual_vol if strategy_annual_vol > 0 else 0
        benchmark_sharpe = (benchmark_annual_return - risk_free_rate) / benchmark_annual_vol if benchmark_annual_vol > 0 else 0

        # 最大回撤
        def calculate_max_drawdown(nav_series):
            peak = nav_series.expanding().max()
            drawdown = (nav_series - peak) / peak
            max_drawdown = drawdown.min()

            # 找到最大回撤的开始和结束时间
            max_dd_end = drawdown.idxmin()
            max_dd_start = nav_series[:max_dd_end].idxmax()

            return max_drawdown, max_dd_start, max_dd_end

        strategy_max_dd, strategy_dd_start, strategy_dd_end = calculate_max_drawdown(data['strategy_nav'])
        benchmark_max_dd, benchmark_dd_start, benchmark_dd_end = calculate_max_drawdown(data['benchmark_nav'])

        # 卡玛比率
        strategy_calmar = strategy_annual_return / abs(strategy_max_dd) if strategy_max_dd != 0 else np.inf
        benchmark_calmar = benchmark_annual_return / abs(benchmark_max_dd) if benchmark_max_dd != 0 else np.inf

        # 主动持仓胜率
        active_positions = data[data['signal'].shift(1) == 1]
        if len(active_positions) > 0:
            win_rate = (active_positions['strategy_return'] > 0).mean()
        else:
            win_rate = 0

        # 保存绩效指标
        self.performance_metrics = {
            '策略年化收益率': strategy_annual_return,
            '基准年化收益率': benchmark_annual_return,
            '策略年化波动率': strategy_annual_vol,
            '基准年化波动率': benchmark_annual_vol,
            '策略夏普比率': strategy_sharpe,
            '基准夏普比率': benchmark_sharpe,
            '策略最大回撤': strategy_max_dd,
            '基准最大回撤': benchmark_max_dd,
            '策略最大回撤开始': strategy_dd_start,
            '策略最大回撤结束': strategy_dd_end,
            '基准最大回撤开始': benchmark_dd_start,
            '基准最大回撤结束': benchmark_dd_end,
            '策略卡玛比率': strategy_calmar,
            '基准卡玛比率': benchmark_calmar,
            '主动持仓胜率': win_rate,
            '策略总收益率': strategy_total_return,
            '基准总收益率': benchmark_total_return,
            '策略最终净值': data['strategy_nav'].iloc[-1],
            '基准最终净值': data['benchmark_nav'].iloc[-1]
        }

        # 打印绩效指标
        print("\n=== 绩效指标 ===")
        for key, value in self.performance_metrics.items():
            if isinstance(value, (int, float)):
                if 'rate' in key.lower() or '收益' in key or '波动' in key or '夏普' in key or '卡玛' in key or '胜率' in key:
                    print(f"{key}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")

        return self

    def create_final_visualizations(self):
        """创建最终可视化图表"""
        print("正在创建最终可视化图表...")

        if not hasattr(self, 'backtest_results'):
            print("请先运行回测！")
            return self

        data = self.backtest_results

        # 设置图表样式
        plt.style.use('default')

        # 1. 净值走势对比图（含买卖信号和回撤阴影）
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14))

        # 主图：净值走势
        ax1.plot(data.index, data['strategy_nav'], label='择时策略净值', linewidth=3, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='中债财富指数', linewidth=3, color='blue', alpha=0.9)

        # 标记买卖信号
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                       color='green', marker='^', s=100, label=f'买入信号 ({len(buy_points)}次)', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                       color='red', marker='v', s=100, label=f'卖出信号 ({len(sell_points)}次)', zorder=5, alpha=0.8)

        # 添加回撤阴影
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak

        # 找到回撤期间
        in_drawdown = strategy_drawdown < -0.002  # 回撤超过0.2%
        if in_drawdown.any():
            ax1.fill_between(data.index, strategy_peak, data['strategy_nav'],
                           where=in_drawdown, alpha=0.3, color='gray', label='策略回撤区域')

        # 标注重要信息
        final_strategy_return = (data['strategy_nav'].iloc[-1] - 1) * 100
        final_benchmark_return = (data['benchmark_nav'].iloc[-1] - 1) * 100

        ax1.set_title(f'中债财富指数择时策略 - 净值走势对比\n策略总收益: {final_strategy_return:.2f}% | 基准总收益: {final_benchmark_return:.2f}%',
                     fontsize=16, fontweight='bold', pad=20)
        ax1.set_ylabel('净值', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图：回撤幅度
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                        alpha=0.7, color='red', label=f'策略最大回撤: {strategy_drawdown.min()*100:.2f}%')

        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                        alpha=0.5, color='blue', label=f'基准最大回撤: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('回撤幅度对比', fontsize=14)
        ax2.set_ylabel('回撤幅度 (%)', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/最终净值走势对比图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 超额收益走势图
        fig, ax = plt.subplots(figsize=(18, 8))

        ax.plot(data.index, data['excess_nav'], linewidth=3, color='green', label='超额收益净值')
        ax.axhline(y=1, color='black', linestyle='--', alpha=0.5, label='基准线')

        # 标记正负超额收益区域
        ax.fill_between(data.index, 1, data['excess_nav'],
                       where=(data['excess_nav'] >= 1), alpha=0.3, color='green', label='正超额收益')
        ax.fill_between(data.index, 1, data['excess_nav'],
                       where=(data['excess_nav'] < 1), alpha=0.3, color='red', label='负超额收益')

        final_excess_return = (data['excess_nav'].iloc[-1] - 1) * 100
        ax.set_title(f'超额收益走势图 - 总超额收益: {final_excess_return:.2f}%', fontsize=16, fontweight='bold')
        ax.set_ylabel('超额收益净值', fontsize=14)
        ax.set_xlabel('日期', fontsize=12)
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/最终超额收益走势图.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("最终可视化图表创建完成！")
        return self

    def export_final_results(self):
        """导出最终结果到Excel"""
        print("正在导出最终结果到Excel...")

        if not hasattr(self, 'performance_metrics') or not hasattr(self, 'backtest_results'):
            print("请先运行回测和绩效计算！")
            return self

        # 创建Excel写入器
        excel_path = f"{self.output_dir}/最终择时策略分析结果.xlsx"

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 1. 绩效指标表
            metrics_df = pd.DataFrame(list(self.performance_metrics.items()),
                                    columns=['指标', '数值'])
            metrics_df.to_excel(writer, sheet_name='绩效指标', index=False)

            # 2. 策略净值表
            nav_df = self.backtest_results[['strategy_nav', 'benchmark_nav', 'excess_nav']].copy()
            nav_df.columns = ['策略净值', '基准净值', '超额收益净值']
            nav_df.to_excel(writer, sheet_name='净值数据')

            # 3. 交易信号表
            signals_df = self.backtest_results[['signal', 'buy_signal', 'sell_signal',
                                              'strategy_return', 'benchmark_return']].copy()
            signals_df.columns = ['持仓信号', '买入信号', '卖出信号', '策略收益率', '基准收益率']
            signals_df.to_excel(writer, sheet_name='交易信号')

        print(f"最终结果已导出到: {excel_path}")
        return self

if __name__ == "__main__":
    print("=== 中债财富指数择时分析 (最终优化版) ===")

    # 初始化分析
    analyzer = BondTimingFinal("/Users/<USER>/Desktop/中债财富指数择时.xlsx")

    try:
        # 执行完整分析流程
        analyzer.load_data()
        analyzer.preprocess_data()
        analyzer.prepare_features()
        analyzer.build_ensemble_model()
        analyzer.generate_optimized_signals()
        analyzer.backtest_strategy()
        analyzer.calculate_performance_metrics()
        analyzer.create_final_visualizations()
        analyzer.export_final_results()

        print("\n=== 最终分析完成 ===")
        print(f"所有结果已保存到: {analyzer.output_dir}")

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

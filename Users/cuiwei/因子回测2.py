import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import warnings
from scipy.stats import ttest_1samp

warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class FactorAnalysisPlatform:
    """
    可转债单因子参数化分析平台（多周期多图形增强版）

    核心功能：
    1. 因子：12个（含双低分位数 dual_low_quantile）
    2. 回测区间：可配置（默认 2021-01-01 起始），内部加载 2020-12-31 后数据以满足滚动窗口
    3. Excel 输出：
       - Sheet1：基准调仓周期下扫描持仓券数（TopN）
       - Sheet2：基准持仓券数下扫描调仓周期
       - Sheet3：基准调仓周期的五分组净值 + 绩效表（含多空差）
       - Sheet4：基准调仓周期的分组滚动(Expanding)绩效与IC统计（每3个月）
       - Sheet5：所有因子（基准调仓周期）累计RankIC
       - Sheet6：基准调仓周期年度区间（2022、2023、2024、2025YTD）绩效与IC
    4. 图表输出：
       对每个因子，在调仓周期 {1W, 2W, 1M} 下分别输出：
         - 五分组净值图（含Benchmark）
         - 累计RankIC时序图
       以及对每个调仓周期（1W, 2W, 1M）分别输出：
         - 所有因子 TopN（基准持仓数）净值曲线与基准净值曲线的对比图（单张）
    """

    def __init__(self,
                 cb_data_path,
                 benchmark_data_path,
                 output_dir,
                 start_date='2021-01-01',
                 end_date='2025-07-11',
                 base_case_period='1M',
                 base_case_size=30):
        # 基本区间设置
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)

        # 输出目录
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)

        # 调仓周期映射
        self.holding_periods = {'1W': 'W-FRI', '2W': '2W-FRI', '1M': 'M', '2M': '2M', '3M': '3M'}
        self.periods_sequence = list(self.holding_periods.keys())  # 用于扫描
        # 需要绘图的调仓周期（题目要求）
        self.plot_periods = ['1W', '2W', '1M']

        # 持仓券数扫描范围
        self.portfolio_sizes = [10,20,30,40,50,60,70,80,90,100]

        # 基准情景（用于Excel Sheet1/2/3/4/6）
        self.base_case_period = base_case_period
        self.base_case_size = base_case_size

        # 因子及排序方向（ascending=True 表示因子值越小越好）
        self.factor_directions = {
            'market_cap': True,
            'cb_balance': True,
            'dual_low': True,
            'pb_ratio': True,
            'cb_rsi_20d': False,
            'stock_reversal_60d': False,
            'amihud_20d': True,
            'mfi_14d': False,
            'vol_risk_premium': False,
            'iv_hv_ratio': False,
            'iv_skew_20d': False,
            'dual_low_quantile': True
        }
        self.factors = list(self.factor_directions.keys())

        print("开始加载数据并预处理 ...")
        self.cb_data, self.benchmark = self._load_data(cb_data_path, benchmark_data_path)
        if self.cb_data is None:
            raise RuntimeError("转债数据加载失败。")
        print("数据加载完成，开始向量化计算全部因子 ...")
        self._compute_all_factors()
        print("因子计算完成，开始预计算未来区间收益矩阵 ...")
        self._prepare_future_returns()
        print("全部预处理完成，可以开始回测。")

    # ======================================================================
    # 数据加载
    # ======================================================================
    def _load_data(self, cb_path, bench_path):
        """
        加载转债数据与基准指数数据
        - 为确保因子滚动窗口稳定，内部从 2020-12-31 开始读取
        - 回测区间内只保留 start_date ~ end_date
        """
        cb_df = pd.read_excel(cb_path, sheet_name=0)
        cb_df.columns = cb_df.columns.str.strip()
        cb_df['交易日期'] = pd.to_datetime(cb_df['交易日期'])
        cb_df.sort_values(['转债代码','交易日期'], inplace=True)

        # 为预留滚动因子计算冗余区间
        full_start = pd.to_datetime('2020-12-31')
        cb_df = cb_df[(cb_df['交易日期']>=full_start) & (cb_df['交易日期']<=pd.to_datetime(self.end_date))]

        # 数值列格式化
        numeric_cols = ['开盘价','最高价','最低价','收盘价','转债余额','转股溢价率','纯债溢价率','双低','剩余期限',
                        '纯债价格','到期赎回价','强赎线','回售线','隐含波动率','正股开盘价','正股最高价','正股最低价',
                        '正股收盘价','成交量','换手率','振幅','总市值','PE(LYR)','PB(LF)','PS(TTM)','股息率','年化波动率']
        for c in numeric_cols:
            if c in cb_df.columns:
                cb_df[c] = pd.to_numeric(cb_df[c], errors='coerce')

        # 基础日度收益/上市天数
        cb_df['转债日收益率'] = cb_df.groupby('转债代码')['收盘价'].pct_change().fillna(0)
        cb_df['正股日收益率'] = cb_df.groupby('转债代码')['正股收盘价'].pct_change().fillna(0)
        cb_df['上市天数'] = cb_df.groupby('转债代码').cumcount()

        # 基准指数
        bench_df = pd.read_excel(bench_path, sheet_name=0)
        bench_df.columns = bench_df.columns.str.strip()
        bench_df['交易日期'] = pd.to_datetime(bench_df['交易日期'])
        bench_df.sort_values('交易日期', inplace=True)
        bench_df = bench_df[(bench_df['交易日期']>=self.start_date) & (bench_df['交易日期']<=self.end_date)]
        bench_df['基准日收益率'] = bench_df['收盘价'].pct_change().fillna(0)
        bench_df['基准净值'] = (1+bench_df['基准日收益率']).cumprod()

        # 仅保留回测区间
        cb_df = cb_df[(cb_df['交易日期']>=self.start_date) & (cb_df['交易日期']<=self.end_date)].copy()
        return cb_df, bench_df

    # ======================================================================
    # 因子计算（全部向量化）
    # ======================================================================
    def _compute_all_factors(self):
        df = self.cb_data
        g = df.groupby('转债代码', group_keys=False)

        # 直接映射的因子
        df['market_cap'] = df['总市值']
        df['cb_balance'] = df['转债余额']
        df['dual_low'] = df['双低']
        df['pb_ratio'] = df['PB(LF)']

        # RSI(20)
        def _rsi(x):
            delta = x['收盘价'].diff()
            gain = delta.where(delta>0,0).rolling(20, min_periods=20).mean()
            loss = -delta.where(delta<0,0).rolling(20, min_periods=20).mean()
            rs = gain / loss
            rsi = 100 - 100/(1+rs)
            rsi[loss==0] = 50
            return rsi
        df['cb_rsi_20d'] = g.apply(_rsi).reset_index(level=0, drop=True)

        # 反转因子：正股60日收益取负
        df['stock_reversal_60d'] = -g['正股收盘价'].pct_change(60)

        # Amihud 20日流动性
        df['amihud_20d'] = g.apply(
            lambda x: (np.abs(x['转债日收益率'])/(x['收盘价']*x['成交量']))
            .rolling(20, min_periods=20).mean()
        ).reset_index(level=0, drop=True)

        # MFI 14
        typical_price = (df['最高价']+df['最低价']+df['收盘价'])/3
        df['_tp_diff'] = typical_price.groupby(df['转债代码']).diff()
        money_flow = typical_price * df['成交量']
        mf_pos = money_flow.where(df['_tp_diff']>0,0).groupby(df['转债代码']).rolling(14, min_periods=14).sum().reset_index(level=0,drop=True)
        mf_neg = money_flow.where(df['_tp_diff']<0,0).groupby(df['转债代码']).rolling(14, min_periods=14).sum().reset_index(level=0,drop=True)
        mfr = mf_pos / mf_neg
        df['mfi_14d'] = 100 - 100/(1+mfr)
        df.loc[mf_neg==0,'mfi_14d'] = 50
        df.drop(columns=['_tp_diff'], inplace=True)

        # 波动率相关
        df['vol_risk_premium'] = df['隐含波动率'] - df['年化波动率']
        df['iv_hv_ratio'] = df['隐含波动率'] / df['年化波动率']
        iv_roll_mean = g['隐含波动率'].rolling(20, min_periods=20).mean().reset_index(level=0, drop=True)
        iv_roll_std = g['隐含波动率'].rolling(20, min_periods=20).std().reset_index(level=0, drop=True)
        df['iv_skew_20d'] = (df['隐含波动率'] - iv_roll_mean) / iv_roll_std
        df.loc[iv_roll_std==0,'iv_skew_20d'] = 0

        # 双低分位数
        df['dual_low_quantile'] = df.groupby('交易日期')['dual_low'].rank(pct=True)

        self.cb_data = df

        # 日度收益矩阵（用于快速组合收益计算）
        self.ret_matrix = self.cb_data.pivot(index='交易日期', columns='转债代码', values='转债日收益率').sort_index()

    # ======================================================================
    # 调仓日、未来收益（区间收益 = (1+r).prod()-1）
    # ======================================================================
    def _get_rebalance_dates(self, period_key):
        rule = self.holding_periods[period_key]
        date_index = pd.Index(sorted(self.cb_data['交易日期'].unique()))
        tmp = pd.Series(1, index=date_index)
        rb_dates = tmp.resample(rule).last().index
        rb_dates = rb_dates[(rb_dates>=self.start_date) & (rb_dates<=self.end_date)]
        return rb_dates.sort_values()

    def _prepare_future_returns(self):
        """
        预计算每个调仓周期下：
        future_returns[period] = DataFrame(index=调仓日t0, columns=转债代码, 值=从t0(不含)到下一个调仓日t1(含)的复合收益)
        """
        self.future_returns = {}
        for period in self.periods_sequence:
            rb_dates = self._get_rebalance_dates(period)
            fr_list, index_list = [], []
            for i in range(len(rb_dates)-1):
                t0, t1 = rb_dates[i], rb_dates[i+1]
                sub = self.ret_matrix.loc[(self.ret_matrix.index>t0) & (self.ret_matrix.index<=t1)]
                if sub.empty:
                    continue
                future_ret = (1+sub).prod()-1
                fr_list.append(future_ret)
                index_list.append(t0)
            self.future_returns[period] = pd.DataFrame(fr_list, index=index_list) if index_list else pd.DataFrame()

    # ======================================================================
    # 绩效与IC统计
    # ======================================================================
    @staticmethod
    def _calc_performance(nav_series: pd.Series):
        """
        根据净值序列计算年化收益/最大回撤/夏普/卡玛。
        """
        nav = nav_series.dropna()
        if len(nav)<2:
            return {'年化收益':0,'最大回撤':0,'夏普比率':0,'卡玛比率':0}
        total_return = nav.iloc[-1]/nav.iloc[0]-1
        days = (nav.index[-1]-nav.index[0]).days
        annual_return = (1+total_return)**(365.25/days)-1 if days>0 else 0
        daily_ret = nav.pct_change().fillna(0)
        vol = daily_ret.std()*np.sqrt(252)
        sharpe = annual_return/vol if vol>0 else 0
        mdd = (nav/nav.cummax()-1).min()
        calmar = annual_return/abs(mdd) if mdd<0 else 0
        return {'年化收益':annual_return,'最大回撤':abs(mdd),'夏普比率':sharpe,'卡玛比率':calmar}

    @staticmethod
    def _calc_ic_stats(ic_series: pd.Series):
        """
        计算IC统计：
        ICIR = mean/std * sqrt(N) （常见定义）
        """
        ic_series = ic_series.dropna()
        if ic_series.empty:
            return {'RankIC_mean':0,'RankIC_std':0,'ICIR':0,'IC_positive':0,'Ttest_p_value':np.nan}
        mean_val = ic_series.mean()
        std_val = ic_series.std()
        icir = mean_val/std_val*np.sqrt(len(ic_series)) if std_val>0 else 0
        ic_pos = (ic_series>0).mean()
        p_val = ttest_1samp(ic_series,0).pvalue if len(ic_series)>1 else np.nan
        return {'RankIC_mean':mean_val,'RankIC_std':std_val,'ICIR':icir,'IC_positive':ic_pos,'Ttest_p_value':p_val}

    # ======================================================================
    # 主流程
    # ======================================================================
    def run_full_analysis(self):
        all_factor_ic = {}                     # Sheet5 累计IC使用（基准情景）
        period_all_factor_nav = {p:{} for p in self.plot_periods}  # 各调仓周期下所有因子TopN净值（用于综合对比图）

        for factor in self.factors:
            print(f"\n{'='*30}\n正在回测因子: {factor}\n{'='*30}")
            ascending = self.factor_directions[factor]

            # Sheet1：基准调仓周期扫描持仓数；获得基准情景数据（TopN）
            sheet1_df, base_case_data = self._scan_portfolio_sizes(factor, ascending)
            # Sheet2：基准持仓数扫描调仓周期
            sheet2_df = self._scan_holding_periods(factor, ascending)

            if base_case_data is not None:
                # 基准情景（base_case_period）分组分析 + 年度区间（Sheet3 / Sheet4 / Sheet6）
                group_data = self._group_analysis(factor, ascending, base_case_data, period=self.base_case_period)
                annual_table = self._annual_performance(base_case_data)

                # 保存基准情景IC序列（Sheet5）
                all_factor_ic[factor] = base_case_data['ic_series']['RankIC']

                # 写Excel
                self._write_factor_report(factor, sheet1_df, sheet2_df, group_data, base_case_data, annual_table)

                # ======================== 图表扩展 ========================
                # 为绘图周期 {1W, 2W, 1M} 生成：分组净值图 + 累计IC图
                for p in self.plot_periods:
                    plot_data = self._compute_period_topn_and_groups(factor, ascending, period=p)
                    if plot_data is None:
                        continue
                    # 记录该因子的 TopN 净值（用于综合对比图）
                    if 'daily_nav' in plot_data:
                        period_all_factor_nav[p][factor] = plot_data['daily_nav']['Portfolio']

                    # 1) 分组净值图
                    self._plot_group_nav(factor, plot_data['group_nav'], period=p)
                    # 2) 累计IC图
                    self._plot_cumulative_ic(factor, plot_data['ic_series'], period=p)
            else:
                print(f"因子 {factor}: 基准情景数据为空，跳过该因子。")

        # ===================== 综合：所有因子净值对比图（每个调仓周期一张） =====================
        for p in self.plot_periods:
            self._plot_all_factors_nav(period_all_factor_nav[p], period=p)

        # Sheet5：所有因子累计IC（基准情景）
        if all_factor_ic:
            self._write_summary_ic(all_factor_ic)

        print("\n回测完成！所有结果已输出。")

    # ======================================================================
    # Sheet1：扫描持仓券数（使用基准调仓周期）
    # ======================================================================
    def _scan_portfolio_sizes(self, factor, ascending):
        period = self.base_case_period
        future_ret_df = self.future_returns.get(period, pd.DataFrame())
        if future_ret_df.empty:
            return pd.DataFrame(), None

        rb_dates = future_ret_df.index.sort_values()
        benchmark_ret = self.benchmark.set_index('交易日期')['基准日收益率']
        size_metrics_rows = []
        daily_returns_dict = {n:[] for n in self.portfolio_sizes}
        period_returns_dict = {n:[] for n in self.portfolio_sizes}
        base_case_data = None
        ic_records = []

        for t0 in rb_dates:
            # 下一个调仓日
            t1_cand = rb_dates[rb_dates>t0]
            if t1_cand.empty: 
                continue
            t1 = t1_cand[0]

            # 截面选股（过滤）
            cross_sec = self.cb_data[self.cb_data['交易日期']==t0]
            cross_sec = cross_sec[(cross_sec['剩余期限']>0.5) & (cross_sec['转债余额']>1) & (cross_sec['上市天数']>60)].dropna(subset=[factor])
            if cross_sec.empty:
                continue
            cross_sec = cross_sec.sort_values(by=factor, ascending=ascending)
            codes_order = list(cross_sec['转债代码'])

            # 未来区间收益用于IC
            future_sec = future_ret_df.loc[t0]
            y_future = future_sec.reindex(codes_order)
            ic_valid = pd.DataFrame({'factor': cross_sec.set_index('转债代码')[factor],'future': y_future}).dropna()
            ic_val = ic_valid.corr(method='spearman').iloc[0,1] if len(ic_valid)>=5 else np.nan
            ic_records.append({'日期':t0,'RankIC':ic_val})

            # 持有期内日度收益矩阵（包含所有候选券）
            sub_daily = self.ret_matrix.loc[(self.ret_matrix.index>t0) & (self.ret_matrix.index<=t1), codes_order]
            if sub_daily.empty:
                continue
            bench_sub = benchmark_ret.loc[(benchmark_ret.index>t0) & (benchmark_ret.index<=t1)].reindex(sub_daily.index).fillna(0)

            # 利用列方向前缀和快速构造不同N组合
            cumsum_returns = sub_daily.cumsum(axis=1)
            for n in self.portfolio_sizes:
                if n>sub_daily.shape[1]:
                    continue
                daily_port_ret = cumsum_returns.iloc[:, n-1] / n
                daily_df = pd.DataFrame({'Portfolio': daily_port_ret, 'Benchmark': bench_sub})
                daily_returns_dict[n].append(daily_df)
                # 记录窗口区间收益（胜率用）
                port_period_ret = (1+daily_port_ret).prod()-1
                bench_period_ret = (1+bench_sub).prod()-1
                period_returns_dict[n].append({'Date':t0,'Portfolio':port_period_ret,'Benchmark':bench_period_ret})

        # IC 序列汇总
        ic_df = pd.DataFrame(ic_records).set_index('日期')
        ic_stats = self._calc_ic_stats(ic_df['RankIC'])

        # 汇总每个N指标
        for n in self.portfolio_sizes:
            if not daily_returns_dict[n]:
                continue
            df_daily = pd.concat(daily_returns_dict[n]).sort_index()
            nav = (1+df_daily).cumprod()
            metrics = self._calc_performance(nav['Portfolio'])
            pr_df = pd.DataFrame(period_returns_dict[n])
            win_rate = (pr_df['Portfolio']>pr_df['Benchmark']).mean() if not pr_df.empty else 0

            size_metrics_rows.append({
                '持仓券数': n,
                '年化收益': metrics['年化收益'],
                '最大回撤': metrics['最大回撤'],
                '夏普比率': metrics['夏普比率'],
                '卡玛比率': metrics['卡玛比率'],
                '胜率': win_rate,
                'RankIC_mean(%)': ic_stats['RankIC_mean']*100,
                'RankIC_std(%)': ic_stats['RankIC_std']*100,
                'ICIR': ic_stats['ICIR'],
                'IC_positive(%)': ic_stats['IC_positive']*100,
                'Ttest': ic_stats['Ttest_p_value']
            })

            # 基准持仓数：保存日度净值、IC序列、窗口收益
            if n==self.base_case_size:
                base_case_data = {
                    'daily_net_value': nav,
                    'ic_series': ic_df,
                    'period_returns': pr_df
                }
        return pd.DataFrame(size_metrics_rows), base_case_data

    # ======================================================================
    # Sheet2：扫描调仓周期（基准持仓数）
    # ======================================================================
    def _scan_holding_periods(self, factor, ascending):
        size = self.base_case_size
        benchmark_ret = self.benchmark.set_index('交易日期')['基准日收益率']
        rows = []
        for period in self.periods_sequence:
            future_ret_df = self.future_returns.get(period, pd.DataFrame())
            if future_ret_df.empty:
                continue
            rb_dates = future_ret_df.index.sort_values()
            ic_records=[]; daily_returns_list=[]; period_returns_list=[]

            for t0 in rb_dates:
                t1_c = rb_dates[rb_dates>t0]
                if t1_c.empty:
                    continue
                t1 = t1_c[0]

                cross_sec = self.cb_data[self.cb_data['交易日期']==t0]
                cross_sec = cross_sec[(cross_sec['剩余期限']>0.5) & (cross_sec['转债余额']>1) & (cross_sec['上市天数']>60)].dropna(subset=[factor])
                if cross_sec.empty:
                    continue
                cross_sec = cross_sec.sort_values(by=factor, ascending=ascending)
                codes_order = list(cross_sec['转债代码'])
                if len(codes_order)<size:
                    continue

                # IC
                future_sec = future_ret_df.loc[t0]
                ic_valid = pd.DataFrame({'factor':cross_sec.set_index('转债代码')[factor],
                                         'future':future_sec.reindex(codes_order)}).dropna()
                ic_val = ic_valid.corr(method='spearman').iloc[0,1] if len(ic_valid)>=5 else np.nan
                ic_records.append({'日期':t0,'RankIC':ic_val})

                # 取前size
                hold_codes = codes_order[:size]
                sub_daily = self.ret_matrix.loc[(self.ret_matrix.index>t0) & (self.ret_matrix.index<=t1), hold_codes]
                if sub_daily.empty:
                    continue
                bench_sub = benchmark_ret.loc[(benchmark_ret.index>t0) & (benchmark_ret.index<=t1)].reindex(sub_daily.index).fillna(0)
                daily_port_ret = sub_daily.mean(axis=1)
                daily_df = pd.DataFrame({'Portfolio':daily_port_ret,'Benchmark':bench_sub})
                daily_returns_list.append(daily_df)
                period_returns_list.append({'Portfolio':(1+daily_port_ret).prod()-1,
                                            'Benchmark':(1+bench_sub).prod()-1})
            if not daily_returns_list:
                continue

            df_daily = pd.concat(daily_returns_list).sort_index()
            nav = (1+df_daily).cumprod()
            metrics = self._calc_performance(nav['Portfolio'])
            pr_df = pd.DataFrame(period_returns_list)
            win_rate = (pr_df['Portfolio']>pr_df['Benchmark']).mean() if not pr_df.empty else 0
            ic_df = pd.DataFrame(ic_records).set_index('日期')
            ic_stats = self._calc_ic_stats(ic_df['RankIC'])
            rows.append({
                '持有周期': period,
                '年化收益': metrics['年化收益'],
                '最大回撤': metrics['最大回撤'],
                '夏普比率': metrics['夏普比率'],
                '卡玛比率': metrics['卡玛比率'],
                '胜率': win_rate,
                'RankIC_mean(%)': ic_stats['RankIC_mean']*100,
                'RankIC_std(%)': ic_stats['RankIC_std']*100,
                'ICIR': ic_stats['ICIR'],
                'IC_positive(%)': ic_stats['IC_positive']*100,
                'Ttest': ic_stats['Ttest_p_value']
            })
        return pd.DataFrame(rows)

    # ======================================================================
    # 分组分析（五等分 + Long/Short），支持任意调仓周期（用于基准情景和绘图）
    # ======================================================================
    def _group_analysis(self, factor, ascending, base_case_data, period=None):
        """
        分组法逻辑：
        - 窗口边界：起始日 + 所有调仓日（最后一个窗口延伸至 end_date）
        - 每个窗口截面：先过滤（若为空放宽为全部），按因子升序排列后手工等分为5组
        - 计算日度收益 → 累计净值（不前向填充）
        - Long/Short：若 ascending=True，多头=Group1 空头=Group5；否则反向
        - 滚动指标：使用基准情景的 ic_series（传入 base_case_data）
        """
        if period is None:
            period = self.base_case_period
        rb_dates = self._get_rebalance_dates(period)
        benchmark_ret = self.benchmark.set_index('交易日期')['基准日收益率']

        window_starts = [self.start_date] + list(rb_dates)
        group_daily_ret_dict = {i:[] for i in range(1,6)}

        # 遍历窗口
        for i in range(len(window_starts)):
            t0 = window_starts[i]
            if i < len(window_starts)-1:
                t1 = window_starts[i+1]
            else:
                t1 = self.end_date

            cross_sec_raw = self.cb_data[self.cb_data['交易日期']==t0]
            filt = cross_sec_raw[(cross_sec_raw['剩余期限']>0.5) & (cross_sec_raw['转债余额']>1) & (cross_sec_raw['上市天数']>60)]
            cross_sec = filt if not filt.empty else cross_sec_raw
            cross_sec = cross_sec.dropna(subset=[factor])
            if cross_sec.empty:
                continue

            cross_sec = cross_sec.sort_values(by=factor, ascending=True).reset_index(drop=True)
            n = len(cross_sec)
            base = n // 5
            rem = n % 5
            sizes = [base + (1 if k < rem else 0) for k in range(5)]
            indices = []
            start = 0
            for sz in sizes:
                indices.append((start, start+sz))
                start += sz

            sub_daily_all = self.ret_matrix.loc[(self.ret_matrix.index>t0) & (self.ret_matrix.index<=t1)]
            if sub_daily_all.empty:
                continue
            for g,(l,r) in enumerate(indices, start=1):
                if l==r:
                    continue
                codes = cross_sec.iloc[l:r]['转债代码'].tolist()
                sub_ret = sub_daily_all[codes].mean(axis=1)
                group_daily_ret_dict[g].append(sub_ret)

        group_nav_df = pd.DataFrame()
        for g in range(1,6):
            if group_daily_ret_dict[g]:
                daily_ret = pd.concat(group_daily_ret_dict[g]).sort_index()
                nav = (1+daily_ret).cumprod()
                group_nav_df[f'Group{g}'] = nav

        benchmark_nav = (1+benchmark_ret).cumprod()
        group_nav_df['Benchmark'] = benchmark_nav
        group_nav_df = group_nav_df.sort_index()

        # 多空净值
        if ascending:
            if 'Group1' in group_nav_df and 'Group5' in group_nav_df:
                group_nav_df['Long_Short'] = group_nav_df['Group1']/group_nav_df['Group5']
        else:
            if 'Group5' in group_nav_df and 'Group1' in group_nav_df:
                group_nav_df['Long_Short'] = group_nav_df['Group5']/group_nav_df['Group1']

        # 绩效 & 胜率（使用月末/月度调仓日胜率逻辑：rb_dates 中相邻区间）
        rb_dates_list = list(rb_dates)
        performance_rows=[]
        for col in group_nav_df.columns:
            perf = self._calc_performance(group_nav_df[col])
            if col=='Benchmark':
                win_rate = np.nan
            else:
                wins=[]
                for j in range(len(rb_dates_list)-1):
                    t0=rb_dates_list[j]; t1=rb_dates_list[j+1]
                    sub_nav = group_nav_df[col].loc[(group_nav_df.index>t0)&(group_nav_df.index<=t1)]
                    sub_bench = group_nav_df['Benchmark'].loc[(group_nav_df.index>t0)&(group_nav_df.index<=t1)]
                    if sub_nav.empty or sub_bench.empty: continue
                    port_ret = sub_nav.iloc[-1]/sub_nav.iloc[0]-1
                    bench_ret = sub_bench.iloc[-1]/sub_bench.iloc[0]-1
                    wins.append(port_ret>bench_ret)
                win_rate = np.mean(wins) if wins else np.nan
            performance_rows.append({'组合':col, **perf, '胜率':win_rate})
        perf_table = pd.DataFrame(performance_rows)

        # 滚动指标：基于基准情景IC（base_case_data）
        ic_series_full = base_case_data['ic_series']['RankIC']
        end_dates = group_nav_df.resample('3M').last().index
        rolling_rows=[]
        for end in end_dates:
            sub_nav_df = group_nav_df.loc[:end]
            if sub_nav_df.shape[0]<2:
                continue
            sub_ic = ic_series_full.loc[ic_series_full.index<=end]
            ic_stats = self._calc_ic_stats(sub_ic)
            for col in [c for c in sub_nav_df.columns if c!='Benchmark']:
                perf = self._calc_performance(sub_nav_df[col])
                rolling_rows.append({
                    '截止日期':end,'组合':col,
                    '年化收益':perf['年化收益'],'最大回撤':perf['最大回撤'],
                    '夏普比率':perf['夏普比率'],'卡玛比率':perf['卡玛比率'],
                    'RankIC_mean(%)':ic_stats['RankIC_mean']*100,
                    'RankIC_std(%)':ic_stats['RankIC_std']*100,
                    'ICIR':ic_stats['ICIR'],
                    'IC_positive(%)':ic_stats['IC_positive']*100,
                    'Ttest':ic_stats['Ttest_p_value']
                })
        rolling_table = pd.DataFrame(rolling_rows)
        return {'group_nav':group_nav_df,'group_performance':perf_table,'rolling_metrics':rolling_table}

    # ======================================================================
    # Sheet6：年度区间绩效（基准情景）
    # ======================================================================
    def _annual_performance(self, base_case_data):
        nav = base_case_data['daily_net_value']
        ic_series = base_case_data['ic_series']['RankIC']
        pr_df = base_case_data['period_returns']  # 含 Date

        segments = [
            ('2022', pd.Timestamp('2022-01-01'), pd.Timestamp('2022-12-31')),
            ('2023', pd.Timestamp('2023-01-01'), pd.Timestamp('2023-12-31')),
            ('2024', pd.Timestamp('2024-01-01'), pd.Timestamp('2024-12-31')),
            ('2025YTD', pd.Timestamp('2025-01-01'), self.end_date)
        ]
        rows=[]
        for label, s, e in segments:
            nav_sub = nav.loc[(nav.index>=s) & (nav.index<=e)]
            if nav_sub.empty:
                continue
            perf = self._calc_performance(nav_sub['Portfolio'])
            ic_sub = ic_series.loc[(ic_series.index>=s) & (ic_series.index<=e)]
            ic_stats = self._calc_ic_stats(ic_sub)
            pr_sub = pr_df[(pr_df['Date']>=s) & (pr_df['Date']<=e)]
            win_rate = (pr_sub['Portfolio']>pr_sub['Benchmark']).mean() if not pr_sub.empty else np.nan
            rows.append({
                '年度区间': label,
                '年化收益': perf['年化收益'],
                '最大回撤': perf['最大回撤'],
                '夏普比率': perf['夏普比率'],
                '卡玛比率': perf['卡玛比率'],
                '胜率': win_rate,
                'RankIC_mean(%)': ic_stats['RankIC_mean']*100,
                'RankIC_std(%)': ic_stats['RankIC_std']*100,
                'ICIR': ic_stats['ICIR'],
                'IC_positive(%)': ic_stats['IC_positive']*100,
                'Ttest': ic_stats['Ttest_p_value']
            })
        return pd.DataFrame(rows)

    # ======================================================================
    # 额外：为绘图周期计算 TopN 日度净值、IC、分组净值
    # ======================================================================
    def _compute_period_topn_and_groups(self, factor, ascending, period):
        """
        用于绘图（不写入Excel）：
        返回 dict: {
            'daily_nav': DataFrame(Portfolio/Benchmark),
            'ic_series': DataFrame(RankIC),
            'group_nav': DataFrame(Group1..Group5, Benchmark, Long_Short)
        }
        """
        future_ret_df = self.future_returns.get(period, pd.DataFrame())
        if future_ret_df.empty:
            return None
        rb_dates = future_ret_df.index.sort_values()
        benchmark_ret = self.benchmark.set_index('交易日期')['基准日收益率']
        size = self.base_case_size

        ic_records=[]
        daily_returns_list=[]
        period_returns_list=[]

        for t0 in rb_dates:
            t1_c = rb_dates[rb_dates>t0]
            if t1_c.empty:
                continue
            t1 = t1_c[0]

            cross_sec = self.cb_data[self.cb_data['交易日期']==t0]
            # 同样过滤（为空则跳过）
            cross_sec = cross_sec[(cross_sec['剩余期限']>0.5) & (cross_sec['转债余额']>1) & (cross_sec['上市天数']>60)].dropna(subset=[factor])
            if cross_sec.empty:
                continue
            cross_sec = cross_sec.sort_values(by=factor, ascending=ascending)
            codes_order = list(cross_sec['转债代码'])
            if len(codes_order)<size:
                continue

            future_sec = future_ret_df.loc[t0]
            ic_valid = pd.DataFrame({'factor':cross_sec.set_index('转债代码')[factor],
                                     'future':future_sec.reindex(codes_order)}).dropna()
            ic_val = ic_valid.corr(method='spearman').iloc[0,1] if len(ic_valid)>=5 else np.nan
            ic_records.append({'日期':t0,'RankIC':ic_val})

            hold_codes = codes_order[:size]
            sub_daily = self.ret_matrix.loc[(self.ret_matrix.index>t0) & (self.ret_matrix.index<=t1), hold_codes]
            if sub_daily.empty:
                continue
            bench_sub = benchmark_ret.loc[(benchmark_ret.index>t0) & (benchmark_ret.index<=t1)].reindex(sub_daily.index).fillna(0)
            daily_port_ret = sub_daily.mean(axis=1)
            daily_df = pd.DataFrame({'Portfolio':daily_port_ret,'Benchmark':bench_sub})
            daily_returns_list.append(daily_df)
            period_returns_list.append({'Portfolio':(1+daily_port_ret).prod()-1,
                                        'Benchmark':(1+bench_sub).prod()-1})

        if not daily_returns_list:
            return None

        df_daily = pd.concat(daily_returns_list).sort_index()
        nav = (1+df_daily).cumprod()
        ic_df = pd.DataFrame(ic_records).set_index('日期')

        # 基于该 period 的 TopN 日度净值构建临时 base_case_data（提供 ic_series 给 group 分析）
        temp_base_case_data = {'ic_series': ic_df}

        # 分组净值（使用该 period 的分组法，但滚动指标使用基准情景ic_series不合适 → 此处直接用自身ic_series）
        group_nav = self._group_analysis_for_plot(factor, ascending, ic_df, period=period)

        return {'daily_nav': nav,
                'ic_series': ic_df,
                'group_nav': group_nav}

    def _group_analysis_for_plot(self, factor, ascending, ic_df, period):
        """
        专供绘图：分组净值（不需要绩效/滚动表，只要 group_nav）
        这里滚动IC不用，直接返回净值 DataFrame。
        """
        rb_dates = self._get_rebalance_dates(period)
        benchmark_ret = self.benchmark.set_index('交易日期')['基准日收益率']

        window_starts = [self.start_date] + list(rb_dates)
        group_daily_ret_dict = {i:[] for i in range(1,6)}

        for i in range(len(window_starts)):
            t0 = window_starts[i]
            if i < len(window_starts)-1:
                t1 = window_starts[i+1]
            else:
                t1 = self.end_date

            cross_sec_raw = self.cb_data[self.cb_data['交易日期']==t0]
            filt = cross_sec_raw[(cross_sec_raw['剩余期限']>0.5) & (cross_sec_raw['转债余额']>1) & (cross_sec_raw['上市天数']>60)]
            cross_sec = filt if not filt.empty else cross_sec_raw
            cross_sec = cross_sec.dropna(subset=[factor])
            if cross_sec.empty:
                continue

            cross_sec = cross_sec.sort_values(by=factor, ascending=True).reset_index(drop=True)
            n = len(cross_sec)
            base = n // 5
            rem = n % 5
            sizes = [base + (1 if k < rem else 0) for k in range(5)]
            indices = []
            start = 0
            for sz in sizes:
                indices.append((start, start+sz))
                start += sz

            sub_daily_all = self.ret_matrix.loc[(self.ret_matrix.index>t0) & (self.ret_matrix.index<=t1)]
            if sub_daily_all.empty:
                continue
            for g,(l,r) in enumerate(indices, start=1):
                if l==r: continue
                codes = cross_sec.iloc[l:r]['转债代码'].tolist()
                sub_ret = sub_daily_all[codes].mean(axis=1)
                group_daily_ret_dict[g].append(sub_ret)

        group_nav_df = pd.DataFrame()
        for g in range(1,6):
            if group_daily_ret_dict[g]:
                daily_ret = pd.concat(group_daily_ret_dict[g]).sort_index()
                nav = (1+daily_ret).cumprod()
                group_nav_df[f'Group{g}'] = nav

        benchmark_nav = (1+benchmark_ret).cumprod()
        group_nav_df['Benchmark'] = benchmark_nav
        group_nav_df = group_nav_df.sort_index()

        if ascending:
            if 'Group1' in group_nav_df and 'Group5' in group_nav_df:
                group_nav_df['Long_Short'] = group_nav_df['Group1']/group_nav_df['Group5']
        else:
            if 'Group5' in group_nav_df and 'Group1' in group_nav_df:
                group_nav_df['Long_Short'] = group_nav_df['Group5']/group_nav_df['Group1']
        return group_nav_df

    # ======================================================================
    # Excel 输出（Sheet1~Sheet6）
    # ======================================================================
    def _write_factor_report(self, factor, sheet1_df, sheet2_df, group_data, base_case_data, annual_table):
        file_path = os.path.join(self.output_dir, f"{factor}_analysis_report.xlsx")
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            if not sheet1_df.empty:
                sheet1_df.to_excel(writer,'vs_PortfolioSize',index=False)
            if not sheet2_df.empty:
                sheet2_df.to_excel(writer,'vs_HoldingPeriod',index=False)

            # Sheet3：分组净值 + 绩效
            group_nav = group_data['group_nav']
            group_nav.to_excel(writer,'Daily_Net_Value')
            perf_table = group_data['group_performance']
            start_row = group_nav.shape[0] + 2
            perf_table.to_excel(writer,'Daily_Net_Value',index=False,startrow=start_row)

            # Sheet4：滚动指标
            rolling_table = group_data['rolling_metrics']
            if not rolling_table.empty:
                rolling_table.to_excel(writer,'Rolling_Metrics',index=False)

            # Sheet6：年度区间
            if not annual_table.empty:
                annual_table.to_excel(writer,'Annual_Performance',index=False)
        print(f"-> 已生成因子 {factor} 报告: {file_path}")

    # ======================================================================
    # Sheet5：所有因子累计IC
    # ======================================================================
    def _write_summary_ic(self, all_factor_ic):
        df_ic = pd.DataFrame(all_factor_ic).sort_index()
        cum_ic = df_ic.cumsum()
        file_path = os.path.join(self.output_dir, "Factor_Overall_Summary.xlsx")
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            cum_ic.to_excel(writer, sheet_name='Cumulative_Rank_IC')
        # 图
        self._plot_dataframe(cum_ic, title='Cumulative Rank IC Comparison (Base Case)',
                             xlabel='Date', ylabel='Cumulative IC',
                             save_path=os.path.join(self.output_dir, "ALL_FACTORS_cumulative_ic.png"))

    # ======================================================================
    # 图形绘制（统一风格）
    # ======================================================================
    def _apply_style(self):
        try:
            plt.style.use('seaborn-v0_8-darkgrid')
        except Exception:
            plt.style.use('ggplot')

    def _plot_group_nav(self, factor, group_nav_df, period):
        """
        绘制单因子在指定调仓周期下的五分组净值图（含Benchmark）
        """
        if group_nav_df.empty:
            return
        self._apply_style()
        fig, ax = plt.subplots(figsize=(12,7))
        plot_cols = [c for c in group_nav_df.columns if c!='Benchmark'] + ['Benchmark']
        group_nav_df[plot_cols].plot(ax=ax, linewidth=1.5,
                                     title=f'Factor {factor} Group Net Value (Period={period})')
        ax.set_xlabel('Date'); ax.set_ylabel('Net Value')
        img_path = os.path.join(self.output_dir, f"{factor}_groups_nav_{period}.png")
        plt.savefig(img_path, dpi=300); plt.close(fig)

    def _plot_cumulative_ic(self, factor, ic_df, period):
        """
        绘制单因子在指定调仓周期下的累计IC时序图
        """
        if ic_df is None or ic_df.empty:
            return
        cum_ic = ic_df['RankIC'].cumsum()
        self._apply_style()
        fig, ax = plt.subplots(figsize=(10,5))
        cum_ic.plot(ax=ax, linewidth=1.5, title=f'Factor {factor} Cumulative Rank IC (Period={period})')
        ax.set_xlabel('Date'); ax.set_ylabel('Cumulative IC')
        img_path = os.path.join(self.output_dir, f"{factor}_cumulative_ic_{period}.png")
        plt.savefig(img_path, dpi=300); plt.close(fig)

    def _plot_all_factors_nav(self, factor_nav_dict, period):
        """
        绘制：指定调仓周期下，所有因子 TopN 组合净值 与 基准净值 对比
        factor_nav_dict: {factor: Series(Portfolio Net Value)}
        """
        if not factor_nav_dict:
            return
        self._apply_style()
        fig, ax = plt.subplots(figsize=(14,8))

        # 基准净值
        benchmark_nav = self.benchmark.set_index('交易日期')['基准净值']
        benchmark_nav.plot(ax=ax, linewidth=2, label='Benchmark')

        # 各因子净值
        for factor, nav_series in factor_nav_dict.items():
            if nav_series.empty:
                continue
            nav_series.plot(ax=ax, linewidth=1, alpha=0.9, label=factor)

        ax.set_title(f'All Factors Top{self.base_case_size} Net Value vs Benchmark (Period={period})')
        ax.set_xlabel('Date'); ax.set_ylabel('Net Value')
        ax.legend(ncol=2, fontsize=8)
        img_path = os.path.join(self.output_dir, f"ALL_FACTORS_nav_compare_{period}.png")
        plt.savefig(img_path, dpi=300); plt.close(fig)

    def _plot_dataframe(self, df, title, xlabel, ylabel, save_path):
        """
        通用 DataFrame 线图绘制
        """
        if df.empty:
            return
        self._apply_style()
        fig, ax = plt.subplots(figsize=(12,7))
        df.plot(ax=ax, linewidth=1.5, title=title)
        ax.set_xlabel(xlabel); ax.set_ylabel(ylabel)
        plt.savefig(save_path, dpi=300); plt.close(fig)


if __name__ == '__main__':
    CB_DATA_PATH = '/Users/<USER>/Desktop/转债数据.xlsx'
    BENCHMARK_DATA_PATH = '/Users/<USER>/Desktop/中证转债指数行情.xlsx'
    OUTPUT_FOLDER_PATH = '/Users/<USER>/Desktop/因子回测结果V1'

    platform = FactorAnalysisPlatform(
        cb_data_path=CB_DATA_PATH,
        benchmark_data_path=BENCHMARK_DATA_PATH,
        output_dir=OUTPUT_FOLDER_PATH,
        start_date='2021-01-01',        # 重新使用 2021/1/1 起点
        end_date='2025-07-11',
        base_case_period='1M',
        base_case_size=30
    )
    platform.run_full_analysis()
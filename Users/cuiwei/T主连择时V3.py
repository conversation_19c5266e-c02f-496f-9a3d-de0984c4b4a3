import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager
import seaborn as sns
import os

# ==============================================================================
# 0. 全局配置
# ==============================================================================
# 数据文件路径
file_path = "/Users/<USER>/Desktop/T主连择时-最终版.xlsx" 

# 使用v9版输出文件夹，代表对S2的最终优化
output_folder = os.path.expanduser("~/Desktop/T_Futures_Timing_Results_v9_Ultimate_S2_Winrate") 

# 回测时间区间
BACKTEST_START_DATE = '2019-01-05'
BACKTEST_END_DATE = '2024-9-24'

# 年化无风险利率
RISK_FREE_RATE = 0.01

# ==============================================================================
# 1. 字体设置
# ==============================================================================
def setup_english_font():
    """强制使用默认的英文字体(DejaVu Sans)以确保图表能跨平台正确显示。"""
    print("正在设置英文字体...")
    try:
        plt.rcParams["font.sans-serif"] = ["DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False
        print("字体设置完成。")
    except Exception as e:
        print(f"字体设置时发生错误: {e}")

# ==============================================================================
# 2. 数据加载与预处理
# ==============================================================================
def load_and_prepare_data(excel_path, backtest_start, backtest_end):
    """加载并处理日频行情和周频指数数据。"""
    print("\n正在加载和预处理数据...")
    if not os.path.exists(excel_path):
        print(f"错误: 无法在以下路径找到文件: {excel_path}")
        return None
    df_daily = pd.read_excel(excel_path, sheet_name='Sheet1')
    df_weekly = pd.read_excel(excel_path, sheet_name='Sheet2')

    df_daily.rename(columns={'日期': 'date', '收盘价': 'close', '开盘价': 'open', '最高价': 'high', '最低价': 'low', '成交量': 'volume'}, inplace=True)
    df_daily['date'] = pd.to_datetime(df_daily['date'])
    df_daily.set_index('date', inplace=True)

    df_weekly.rename(columns={'日期': 'date', '利率综合领先指数': 'leading_index'}, inplace=True)
    df_weekly['date'] = pd.to_datetime(df_weekly['date'])
    df_weekly.set_index('date', inplace=True)
    df_weekly.sort_index(inplace=True)

    df_merged = pd.merge_asof(df_daily.sort_index(), df_weekly, left_index=True, right_index=True, direction='backward')
    df_merged['leading_index'] = df_merged['leading_index'].ffill()
    df_merged['daily_return'] = df_merged['close'].pct_change()
    
    df_backtest = df_merged.loc[backtest_start:backtest_end].copy()
    df_backtest['benchmark_net_value'] = (1 + df_backtest['daily_return'].fillna(0)).cumprod()
    
    print("数据加载与预处理完成。")
    return df_backtest

# ==============================================================================
# 3. 策略实现 (v9)
# ==============================================================================

def run_strategy_1_ultimate_overfit(df):
    """策略1 (终极过拟合版): 保持不变"""
    print("运行策略1 (终极过拟合版)...")
    df_strat = df.copy()
    window = 260
    rolling_mean = df_strat['leading_index'].rolling(window=window, min_periods=20).mean()
    rolling_std = df_strat['leading_index'].rolling(window=window, min_periods=20).std()
    df_strat['z_score'] = (df_strat['leading_index'] - rolling_mean) / rolling_std
    z_ma_fast_period = 4; z_ma_slow_period = 12
    df_strat['z_ma_fast'] = df_strat['z_score'].rolling(window=z_ma_fast_period).mean()
    df_strat['z_ma_slow'] = df_strat['z_score'].rolling(window=z_ma_slow_period).mean()
    df_strat['position'] = 0; position = 0; level_threshold = 0.5
    for i in range(1, len(df_strat)):
        is_golden_cross = df_strat['z_ma_fast'].iloc[i] > df_strat['z_ma_slow'].iloc[i] and df_strat['z_ma_fast'].iloc[i-1] <= df_strat['z_ma_slow'].iloc[i-1]
        is_death_cross = df_strat['z_ma_fast'].iloc[i] < df_strat['z_ma_slow'].iloc[i] and df_strat['z_ma_fast'].iloc[i-1] >= df_strat['z_ma_slow'].iloc[i-1]
        if is_golden_cross and df_strat['z_score'].iloc[i] < -level_threshold: position = 1
        elif is_death_cross and df_strat['z_score'].iloc[i] > level_threshold: position = -1
        elif (position == 1 and is_death_cross): position = 0
        elif (position == -1 and is_golden_cross): position = 0
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'].fillna(0, inplace=True)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    return df_strat

# ULTIMATE WIN-RATE STRATEGY 2
def run_strategy_2_ultimate_winrate(df):
    """策略2 (终极胜率版): 基于布林带的双重确认均值回归策略"""
    print("运行策略2 (终极胜率版)...")
    df_strat = df.copy()

    # --- 1. 计算指标：布林带 ---
    bb_period = 20; bb_std_dev = 2.2
    df_strat['bb_middle'] = df_strat['close'].rolling(window=bb_period).mean()
    df_strat['bb_std'] = df_strat['close'].rolling(window=bb_period).std()
    df_strat['bb_upper'] = df_strat['bb_middle'] + (df_strat['bb_std'] * bb_std_dev)
    df_strat['bb_lower'] = df_strat['bb_middle'] - (df_strat['bb_std'] * bb_std_dev)
    
    # --- 2. 生成交易信号 (双重确认逻辑) ---
    df_strat['position'] = 0
    position = 0
    for i in range(1, len(df_strat)): # 从1开始，因为我们需要i-1的数据
        # 继承前一天的仓位
        current_position = position

        # --- a. 退出逻辑 (优先级最高) ---
        # 如果持有多仓，当价格回归到中轨时平仓
        if current_position == 1 and df_strat['close'].iloc[i] > df_strat['bb_middle'].iloc[i]:
            position = 0
        # 如果持有空仓，当价格回归到中轨时平仓
        elif current_position == -1 and df_strat['close'].iloc[i] < df_strat['bb_middle'].iloc[i]:
            position = 0

        # --- b. 入场逻辑 (仅在空仓时判断) ---
        if current_position == 0:
            # 做多入场：必须满足(1)昨天收盘价在下轨之下 (2)今天收盘价回到下轨之上
            if (df_strat['close'].iloc[i-1] < df_strat['bb_lower'].iloc[i-1] and 
                df_strat['close'].iloc[i] > df_strat['bb_lower'].iloc[i]):
                position = 1
            # 做空入场：必须满足(1)昨天收盘价在上轨之上 (2)今天收盘价回到上轨之下
            elif (df_strat['close'].iloc[i-1] > df_strat['bb_upper'].iloc[i-1] and
                  df_strat['close'].iloc[i] < df_strat['bb_upper'].iloc[i]):
                position = -1
        
        # 更新当天的最终仓位
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position

    # --- 3. 计算策略收益（已集成无风险利率） ---
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'].fillna(0, inplace=True)
    
    # --- 4. 计算净值曲线 ---
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    return df_strat


def run_strategy_3_advanced_combination(df_s1, df_s2):
    """策略3 (高级组合版): 保持不变"""
    print("运行策略3 (高级组合版)...")
    # ... (代码与您版本相同) ...
    df_strat = df_s1.copy()
    macro_signal_col = df_s1['position']
    tech_signal_col = df_s2['position']
    df_strat['position'] = 0; position = 0
    for i in range(1, len(df_strat)):
        macro_signal = macro_signal_col.iloc[i]
        tech_signal = tech_signal_col.iloc[i]
        current_position = position
        if current_position == 0:
            if macro_signal == 1 and tech_signal == 1: position = 1
            elif macro_signal == -1 and tech_signal == -1: position = -1
        else:
            if current_position == 1 and macro_signal != 1: position = 0
            elif current_position == -1 and macro_signal != -1: position = 0
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'].fillna(0, inplace=True)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    return df_strat

# ==============================================================================
# 4. 性能分析与报告生成
# ==============================================================================
def calculate_performance_metrics(df, strategy_name):
    """计算详细的量化回测指标。"""
    # ... (代码与您版本相同) ...
    try:
        if df['strategy_net_value'].iloc[-1] == 1 and df['strategy_return'].abs().sum() < 1e-9:
             return pd.Series({ 'Annual Return': '0.00%', 'Annual Volatility': '0.00%', 'Sharpe Ratio': '0.00', 'Max Drawdown': '0.00%', 'Calmar Ratio': '0.00', 'Win Rate': '0.00%' }, name=strategy_name)
    except Exception: pass
    total_days = len(df); trading_days_per_year = 252
    annual_return = (df['strategy_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
    annual_volatility = df['strategy_return'].std() * np.sqrt(trading_days_per_year)
    sharpe_ratio = (annual_return - RISK_FREE_RATE) / annual_volatility if annual_volatility != 0 else 0
    df['cumulative_max'] = df['strategy_net_value'].cummax()
    df['drawdown'] = (df['strategy_net_value'] - df['cumulative_max']) / df['cumulative_max']
    max_drawdown = df['drawdown'].min()
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
    if 'position' in df.columns:
        trade_days = df[df['position'].shift(1) != 0]
        daily_rf = RISK_FREE_RATE / 252
        win_rate = (trade_days['strategy_return'] > daily_rf).sum() / len(trade_days) if len(trade_days) > 0 else 0
    else:
        win_rate = (df['strategy_return'] > 0).sum() / len(df) if len(df) > 0 else 0
    return pd.Series({ 'Annual Return': f"{annual_return:.2%}", 'Annual Volatility': f"{annual_volatility:.2%}", 'Sharpe Ratio': f"{sharpe_ratio:.2f}", 'Max Drawdown': f"{max_drawdown:.2%}", 'Calmar Ratio': f"{calmar_ratio:.2f}", 'Win Rate': f"{win_rate:.2%}" }, name=strategy_name)

def generate_report(df, strategy_name_en, output_folder_path):
    """生成并保存所有图表，使用英文标签。"""
    # ... (代码与您版本相同) ...
    print(f"\n--- 正在为 '{strategy_name_en}' 生成报告 ---")
    df = df.copy(); df['cumulative_max'] = df['strategy_net_value'].cummax()
    fig, ax1 = plt.subplots(figsize=(16, 8))
    df['strategy_net_value'].plot(ax=ax1, label=strategy_name_en, color='crimson', lw=2)
    df['benchmark_net_value'].plot(ax=ax1, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax1.set_title(f'Cumulative Net Value: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax1.set_ylabel('Cumulative Net Value'); ax1.set_xlabel('Date')
    ax1.grid(True, alpha=0.4); ax1.legend(loc='upper left')
    ax1.fill_between(df.index, df['strategy_net_value'], df['cumulative_max'], where=df['strategy_net_value'] < df['cumulative_max'], color='crimson', alpha=0.2)
    plt.tight_layout(); plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_1_NetValue.png")); plt.close(fig)
    strat_monthly = df['strategy_return'].resample('ME').apply(lambda r: (1+r).prod()-1); bench_monthly = df['daily_return'].resample('ME').apply(lambda r: (1+r).prod()-1)
    monthly_returns = pd.DataFrame({strategy_name_en: strat_monthly, 'Benchmark': bench_monthly})
    fig, ax = plt.subplots(figsize=(16, 8)); monthly_returns.plot(kind='bar', ax=ax, width=0.8)
    ax.set_title(f'Monthly Returns Distribution: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax.set_ylabel('Monthly Return'); ax.set_xlabel('Month')
    ax.axhline(0, color='grey', linewidth=0.8); ax.xaxis.set_major_formatter(plt.FixedFormatter(monthly_returns.index.strftime('%Y-%m')))
    plt.xticks(rotation=45); plt.tight_layout(); plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_2_MonthlyReturns.png")); plt.close(fig)
    df['excess_return'] = df['strategy_return'] - df['daily_return']
    df['cumulative_excess_return'] = (1 + df['excess_return']).cumprod() - 1
    fig, ax = plt.subplots(figsize=(16, 8)); df['cumulative_excess_return'].plot(ax=ax, color='green', lw=2)
    ax.set_title(f'Cumulative Excess Return vs. Benchmark: {strategy_name_en}', fontsize=18)
    ax.set_ylabel('Cumulative Excess Return'); ax.set_xlabel('Date')
    ax.grid(True, alpha=0.4); ax.axhline(0, color='grey', linewidth=0.8)
    plt.tight_layout(); plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_3_ExcessReturn.png")); plt.close(fig)
    print(f"'{strategy_name_en}' 的所有图表已保存。")
    metrics = calculate_performance_metrics(df, strategy_name_en)
    base_df = df[['daily_return', 'benchmark_net_value']].rename(columns={'benchmark_net_value': 'strategy_net_value', 'daily_return': 'strategy_return'})
    base_metrics = calculate_performance_metrics(base_df, 'Benchmark')
    return metrics, base_metrics

# ==============================================================================
# 5. 主执行流程
# ==============================================================================
def main():
    """主函数，串联所有流程。"""
    print("脚本开始执行...")
    setup_english_font()
    if not os.path.exists(output_folder): os.makedirs(output_folder)
    print(f"结果将保存至: {output_folder}")
    
    base_df = load_and_prepare_data(file_path, BACKTEST_START_DATE, BACKTEST_END_DATE)
    if base_df is None: return
        
    # --- 运行最终的、集成了所有优化的策略 ---
    s1_df = run_strategy_1_ultimate_overfit(base_df)
    s2_df = run_strategy_2_ultimate_winrate(base_df) # 调用最新的S2策略
    s3_df = run_strategy_3_advanced_combination(s1_df, s2_df)
    
    # --- 为报告和文件名定义英文策略名称 ---
    s1_name = 'S1_Ultimate_Overfit_Rate_Index'
    s2_name = 'S2_Ultimate_Winrate_MeanReversion' # 更新S2名称
    s3_name = 'S3_Ultimate_Combination'
    
    # --- 生成报告 ---
    s1_metrics, base_metrics = generate_report(s1_df, s1_name, output_folder)
    s2_metrics, _ = generate_report(s2_df, s2_name, output_folder)
    s3_metrics, _ = generate_report(s3_df, s3_name, output_folder)
    
    # --- 生成额外的对比图 ---
    print("\n--- 正在生成额外的对比图 ---")
    fig, ax = plt.subplots(figsize=(16, 8))
    s2_df['strategy_net_value'].plot(ax=ax, label=s2_name, color='darkorange', lw=2)
    s3_df['strategy_net_value'].plot(ax=ax, label=s3_name, color='purple', lw=2)
    base_df['benchmark_net_value'].plot(ax=ax, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax.set_title('Net Value Comparison: Ultimate S2 vs. S3', fontsize=18)
    ax.set_ylabel('Cumulative Net Value'); ax.grid(True, alpha=0.4); ax.legend(loc='upper left')
    plt.tight_layout(); plot_path = os.path.join(output_folder, "Comparison_S2_vs_S3_Ultimate.png"); plt.savefig(plot_path); plt.close(fig)
    print(f"对比图已保存: {plot_path}")

    # --- 将所有指标汇总到Excel文件 ---
    all_metrics = pd.concat([base_metrics.to_frame().T, s1_metrics.to_frame().T, s2_metrics.to_frame().T, s3_metrics.to_frame().T])
    
    excel_output_path = os.path.join(output_folder, 'Backtest_Results_v9_Ultimate_S2.xlsx')
    with pd.ExcelWriter(excel_output_path, engine='openpyxl') as writer:
        all_metrics.to_excel(writer, sheet_name='Performance_Summary')
        s1_df.to_excel(writer, sheet_name=f'{s1_name}_Data')
        s2_df.to_excel(writer, sheet_name=f'{s2_name}_Data')
        s3_df.to_excel(writer, sheet_name=f'{s3_name}_Data')
    print(f"\n所有结果已保存至Excel文件: {excel_output_path}")
    
    print("\n\n*** 所有任务执行完毕! ***")

if __name__ == "__main__":
    main()
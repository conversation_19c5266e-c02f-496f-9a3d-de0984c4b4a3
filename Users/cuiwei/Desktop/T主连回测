# -*- coding: utf-8 -*-
"""
T主连择时 | 周频回测（不做空、无成本、Rf=0）
数据目录：
  /Users/<USER>/Desktop/T主连择时-最终版
    - sheet1: 日频 T主连行情（已过滤非交易日）
      列：日期 收盘价 结算价 开盘价 最高价 最低价 成交量 成交额 涨停价 跌停价 持仓量
    - sheet2: 周频 利率综合领先指数
      列：日期 利率综合领先指数

回测：
  - 信号在周t收盘形成，作用于 t→t+1 周收益
  - 不做空，忽略交易成本，Rf=0
  - 所有 Z-score / 分位 的滚窗 = 52W
  - CLI 使用 t-4（严格不可前视），默认“差分→robust z”后取反号
  - 三策略：纯CLI、技术因子、CLI+技术（多数投票+置信度缩放），均为 Long/Flat

输出：
  /Users/<USER>/Desktop/T主连量化择时结果/
    - 每策略：净值对比&最大回撤阴影、月度收益分布直方图、月度收益时间序列柱状图、累计超额收益
    - 每策略：周/月度明细与指标（xlsx）
    - 汇总：三策略与基准对比指标表（xlsx）
"""

from __future__ import annotations
import os, math, warnings
from pathlib import Path
from typing import Tuple, Dict

import numpy as np
import pandas as pd
import matplotlib
import matplotlib.pyplot as plt

warnings.filterwarnings("ignore")

# ==============================
# 全局参数 & 路径
# ==============================
DATA_FILE  = Path("/Users/<USER>/Desktop/T主连择时-最终版.xlsx")
RESULT_DIR = Path("/Users/<USER>/Desktop/T主连量化择时结果")

# 频率与窗口
WEEK_FREQ      = "W-SAT"  # 与CLI对齐，周六为周末
ROLL_WIN_W     = 52       # 全部z/分位窗口
CLI_LAG_W      = 4        # CLI滞后
EMA_SPAN       = 20       # EMA(20)
MACD_FAST      = 12       # MACD 12
MACD_SLOW      = 26       # MACD 26
MACD_SIGNAL    = 9        # MACD 9
MOM_LAG_W      = 4        # MOM4
ATR_SPAN       = 14       # ATR14
RVOL_WIN       = 8        # RVOL8

# 阈值（保持原意）
EMA_BAND       = 0.002    # EMA缓冲带 ±0.2%
MOM_TH         = 0.002    # MOM4阈值 ±0.2%
RVOL_POS       = 1.10
RVOL_NEG       = 0.90
VOI_POS_Q      = 0.60
VOI_NEG_Q      = 0.40
CLI_POS_Z      = -0.5     # S_cli<=-0.5 多；S_cli>=+0.5 观望；|S_cli|<0.25 中性
CLI_NEG_Z      = +0.5
CLI_NEU_Z      = 0.25

# 噪声权重分位
ATR_P85, ATR_P95 = 0.85, 0.95
AMI_P70, AMI_P90 = 0.70, 0.90

# 回测区间
BT_START = pd.Timestamp("2019-01-05")
BT_END   = pd.Timestamp("2025-08-09")
FORCE_BEGIN = pd.Timestamp("2020-01-01")  # 你要求：移动平均与z窗52W，实测从2020年起回测

# ==============================
# 字体设置（中文优先）
# ==============================
def setup_chinese_font():
    candidates = [
        "/System/Library/Fonts/PingFang.ttc",
        "/Library/Fonts/NotoSansSC-Regular.otf",
        "/System/Library/Fonts/STHeiti Medium.ttc",
        "/System/Library/Fonts/Hiragino Sans GB W3.otf",
    ]
    picked = None
    for p in candidates:
        if os.path.exists(p):
            try:
                matplotlib.font_manager.fontManager.addfont(p)
                picked = matplotlib.font_manager.FontProperties(fname=p).get_name()
                break
            except Exception:
                continue
    plt.rcParams["font.sans-serif"] = [picked or "DejaVu Sans"]
    plt.rcParams["axes.unicode_minus"] = False

setup_chinese_font()

# ==============================
# 工具函数
# ==============================
def ensure_dir(p: Path) -> None:
    p.mkdir(parents=True, exist_ok=True)

def rolling_mad(a: np.ndarray) -> float:
    med = np.median(a)
    return np.median(np.abs(a - med))

def robust_z(s: pd.Series, win: int) -> pd.Series:
    med = s.rolling(win, min_periods=win).median()
    mad = s.rolling(win, min_periods=win).apply(rolling_mad, raw=True)
    return (s - med) / (1.4826 * mad.replace(0, np.nan))

def max_drawdown(nav: pd.Series) -> Tuple[float, pd.Timestamp, pd.Timestamp]:
    roll_max = nav.cummax()
    dd = nav / roll_max - 1.0
    mdd = dd.min()
    end = dd.idxmin()
    start = (nav.loc[:end]).idxmax()
    return float(mdd), start, end

def cagr(weekly_ret: pd.Series) -> float:
    r = weekly_ret.dropna()
    if r.empty: return 0.0
    nav = (1 + r).cumprod()
    years = r.size / 52.0
    return float(nav.iloc[-1] ** (1 / years) - 1) if years > 0 else 0.0

def ann_vol(weekly_ret: pd.Series) -> float:
    return float(weekly_ret.std(ddof=0) * math.sqrt(52))

def sharpe(weekly_ret: pd.Series) -> float:
    if weekly_ret.std(ddof=0) == 0 or weekly_ret.dropna().empty: return 0.0
    return float(weekly_ret.mean() / weekly_ret.std(ddof=0) * math.sqrt(52))

def calmar(weekly_ret: pd.Series) -> float:
    mdd, _, _ = max_drawdown((1 + weekly_ret.fillna(0)).cumprod())
    return cagr(weekly_ret) / abs(mdd) if mdd < 0 else np.nan

def info_ratio(weekly_ret: pd.Series, bench_ret: pd.Series) -> float:
    diff = (weekly_ret - bench_ret).dropna()
    if diff.std(ddof=0) == 0 or diff.empty: return 0.0
    return float(diff.mean() / diff.std(ddof=0) * math.sqrt(52))

def monthly_from_weekly(weekly_ret: pd.Series) -> pd.Series:
    df = weekly_ret.dropna().to_frame("ret")
    df["m"] = df.index.to_period("M")
    out = df.groupby("m")["ret"].apply(lambda x: (1 + x).prod() - 1)
    out.index = out.index.to_timestamp("M")
    return out

# ==============================
# 数据处理
# ==============================
def load_excel_pair(data_file: Path) -> tuple[pd.DataFrame, pd.DataFrame]:
    """从Excel文件加载两个sheet的数据"""
    assert data_file.exists(), f"文件不存在：{data_file}"

    print(f"正在加载数据文件：{data_file}")
    df_d = pd.read_excel(data_file, sheet_name=0)
    df_c = pd.read_excel(data_file, sheet_name=1)

    print(f"Sheet 0 列名: {df_d.columns.tolist()}")
    print(f"Sheet 1 列名: {df_c.columns.tolist()}")

    df_d.rename(columns=lambda c: str(c).strip(), inplace=True)
    df_c.rename(columns=lambda c: str(c).strip(), inplace=True)
    df_d["日期"] = pd.to_datetime(df_d["日期"])
    df_c["日期"] = pd.to_datetime(df_c["日期"])

    print(f"日频数据形状: {df_d.shape}, 时间范围: {df_d['日期'].min()} 到 {df_d['日期'].max()}")
    print(f"CLI数据形状: {df_c.shape}, 时间范围: {df_c['日期'].min()} 到 {df_c['日期'].max()}")

    return df_d, df_c

def to_weekly(df_daily: pd.DataFrame) -> pd.DataFrame:
    df = df_daily.set_index("日期").sort_index()
    w = pd.DataFrame(index=pd.date_range(df.index.min(), df.index.max(), freq=WEEK_FREQ))
    # 价
    w["收盘价"] = df["收盘价"].resample(WEEK_FREQ).last()
    w["开盘价"] = df["开盘价"].resample(WEEK_FREQ).first()
    w["最高价"] = df["最高价"].resample(WEEK_FREQ).max()
    w["最低价"] = df["最低价"].resample(WEEK_FREQ).min()
    w["结算价"] = df["结算价"].resample(WEEK_FREQ).last()
    # 量/额/持仓
    w["成交量"] = df["成交量"].resample(WEEK_FREQ).sum()
    w["成交额"] = df["成交额"].resample(WEEK_FREQ).sum()
    w["持仓量_last"] = df["持仓量"].resample(WEEK_FREQ).last()
    w["持仓量_mean"] = df["持仓量"].resample(WEEK_FREQ).mean()
    # Amihud用
    w["_sum_abs_ret"]  = df["收盘价"].pct_change().abs().resample(WEEK_FREQ).sum()
    w["_sum_turnover"] = (df["成交量"] * df["收盘价"]).resample(WEEK_FREQ).sum()
    return w

def tech_indicators(w: pd.DataFrame) -> pd.DataFrame:
    out = w.copy()
    close = out["收盘价"]

    out["ret_w"] = close.pct_change()
    out["EMA20"] = close.ewm(span=EMA_SPAN, adjust=False).mean()

    ema12 = close.ewm(span=MACD_FAST, adjust=False).mean()
    ema26 = close.ewm(span=MACD_SLOW, adjust=False).mean()
    macd = ema12 - ema26
    out["MACD_hist"] = macd - macd.ewm(span=MACD_SIGNAL, adjust=False).mean()

    out["MOM4"] = close / close.shift(MOM_LAG_W) - 1.0
    out["VOI"]  = out["成交量"] / out["持仓量_mean"].replace(0, np.nan)
    out["RVOL8"] = out["成交量"] / (out["成交量"].shift(1).rolling(RVOL_WIN, min_periods=RVOL_WIN).mean())

    prev_close = close.shift(1)
    tr = pd.concat([(out["最高价"] - out["最低价"]),
                    (out["最高价"] - prev_close).abs(),
                    (out["最低价"] - prev_close).abs()], axis=1).max(axis=1)
    out["ATR14"] = tr.ewm(span=ATR_SPAN, adjust=False).mean()
    out["ATR_ratio"] = out["ATR14"] / close

    out["Amihud_w"] = out["_sum_abs_ret"] / out["_sum_turnover"].replace(0, np.nan)
    return out

def noise_weight(df: pd.DataFrame, win: int = ROLL_WIN_W) -> pd.Series:
    atr, ami = df["ATR_ratio"], df["Amihud_w"]
    roll_q = lambda s, q: s.rolling(win, min_periods=win).quantile(q)
    atr85, atr95 = roll_q(atr, ATR_P85), roll_q(atr, ATR_P95)
    ami70, ami90 = roll_q(ami, AMI_P70), roll_q(ami, AMI_P90)

    w = pd.Series(1.0, index=df.index)
    w[(atr >= atr95) | (ami >= ami90)] = 0.0
    mask_half = (atr >= atr85) | (ami >= ami70)
    w[(w == 1.0) & mask_half] = 0.5
    return w

def cli_signal(cli_week: pd.DataFrame, weekly_index: pd.DatetimeIndex,
               win: int = ROLL_WIN_W, lag: int = CLI_LAG_W,
               method: str = "diff_robust_z") -> pd.Series:
    """返回连续 S_cli（已取反号），index=周六"""
    s = cli_week.set_index("日期").sort_index().reindex(weekly_index)["利率综合领先指数"]
    if method == "diff_robust_z":
        z = robust_z(s.diff(), win)
    elif method == "level_robust_z":
        z = robust_z(s, win)
    else:
        raise ValueError("method must be 'diff_robust_z' or 'level_robust_z'")
    return (-z).shift(lag)

# ==============================
# 信号构建（离散化）
# ==============================
def tech_signals(df: pd.DataFrame) -> Dict[str, pd.Series]:
    close, ema20 = df["收盘价"], df["EMA20"]
    hist = df["MACD_hist"]; dh = hist.diff()

    # EMA 带缓冲
    S_EMA = pd.Series(np.where(close >= ema20*(1+EMA_BAND), 1,
                        np.where(close <= ema20*(1-EMA_BAND), -1, 0)), index=df.index)
    # MACD 柱体 & 增长
    S_MACD = pd.Series(np.where((hist >= 0) & (dh >= 0), 1,
                         np.where((hist <= 0) & (dh <= 0), -1, 0)), index=df.index)
    # MOM4
    mom = df["MOM4"]
    S_MOM = pd.Series(np.where(mom >= MOM_TH, 1, np.where(mom <= -MOM_TH, -1, 0)), index=df.index)

    # 趋势/动量多数票
    votes_long  = (S_EMA.eq(1) + S_MACD.eq(1) + S_MOM.eq(1))
    votes_short = (S_EMA.eq(-1) + S_MACD.eq(-1) + S_MOM.eq(-1))
    S_TM = pd.Series(np.where(votes_long >= 2, 1, np.where(votes_short >= 2, -1, 0)), index=df.index)

    # VOI分位
    def pct_rank_last(x: pd.Series) -> float:
        r = x.rank(method="average")
        return float(r.iloc[-1] / len(r))
    VOI_rank = df["VOI"].rolling(ROLL_WIN_W, min_periods=ROLL_WIN_W).apply(pct_rank_last, raw=False)
    S_VOI  = pd.Series(np.where(VOI_rank >= VOI_POS_Q, 1,
                         np.where(VOI_rank <= VOI_NEG_Q, -1, 0)), index=df.index)
    S_RVOL = pd.Series(np.where(df["RVOL8"] >= RVOL_POS, 1,
                         np.where(df["RVOL8"] <= RVOL_NEG, -1, 0)), index=df.index)
    # 量能确认（任一为 +1 即通过；两个都 -1 则 -1；其余 0）
    S_VOL = pd.Series(np.where((S_VOI.eq(1)) | (S_RVOL.eq(1)), 1,
                        np.where((S_VOI.eq(-1)) & (S_RVOL.eq(-1)), -1, 0)), index=df.index)
    return dict(S_EMA=S_EMA, S_MACD=S_MACD, S_MOM=S_MOM, S_TM=S_TM, S_VOI=S_VOI, S_RVOL=S_RVOL, S_VOL=S_VOL)

# ==============================
# 仓位（不做空；信号在t生效于t+1）
# ==============================
def pos_cli(S_cli: pd.Series, W_noise: pd.Series) -> pd.Series:
    pos = pd.Series(0.0, index=S_cli.index)
    pos[S_cli <= CLI_POS_Z] = 1.0
    pos[(S_cli >= CLI_NEG_Z) | (S_cli.abs() < CLI_NEU_Z)] = 0.0
    return (pos * W_noise).clip(0.0, 1.0).shift(1)

def pos_tech(S_TM: pd.Series, S_VOL: pd.Series, W_noise: pd.Series) -> pd.Series:
    pos = pd.Series(np.where((S_TM.eq(1)) & (S_VOL.eq(1)), 1.0, 0.0), index=S_TM.index)
    return (pos * W_noise).clip(0.0, 1.0).shift(1)

def pos_combo(S_cli: pd.Series, S_TM: pd.Series, S_VOL: pd.Series, W_noise: pd.Series) -> pd.Series:
    # s1：CLI → +1 / 0 / -1（Long/Flat最终只用 +1 或 0）
    s1 = pd.Series(np.where(S_cli <= CLI_POS_Z, 1, np.where(S_cli >= CLI_NEG_Z, -1, 0)), index=S_cli.index)
    # s2：技术通过才 +1
    s2 = pd.Series(np.where((S_TM.eq(1)) & (S_VOL.eq(1)), 1, 0), index=S_TM.index)
    # 多数投票（Long/Flat：负票当作0，仅拒绝多头）
    vote = s1 + s2
    base = (vote >= 1).astype(float)
    # 置信度：一致且 |S_cli|≥0.8 →1.25；一致→1.0；仅一方→0.75；否则0
    conf = np.where((s1.eq(1)) & (s2.eq(1)) & (S_cli.abs() >= 0.8), 1.25,
             np.where((s1.eq(1)) & (s2.eq(1)), 1.00,
             np.where((s1.eq(1)) ^ (s2.eq(1)), 0.75, 0.0)))
    pos = (base * conf)
    return (pd.Series(pos, index=S_cli.index) * W_noise).clip(0.0, 1.0).shift(1)

# ==============================
# 回测 & 评估 & 作图
# ==============================
def backtest(weekly_close: pd.Series, pos: pd.Series) -> tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    ret_w = weekly_close.pct_change().fillna(0)
    strat_ret = (pos.fillna(0) * ret_w).fillna(0)
    bench_ret = ret_w
    return strat_ret, (1 + strat_ret).cumprod(), bench_ret, (1 + bench_ret).cumprod()

def perf_table(strat_ret: pd.Series, bench_ret: pd.Series) -> pd.Series:
    active = strat_ret != 0
    mdd, dd_s, dd_e = max_drawdown((1 + strat_ret).cumprod())
    return pd.Series({
        "样本周数": strat_ret.dropna().size,
        "CAGR": cagr(strat_ret),
        "年化波动": ann_vol(strat_ret),
        "夏普": sharpe(strat_ret),
        "最大回撤": mdd,
        "最长回撤开始": dd_s,
        "最长回撤结束": dd_e,
        "卡玛比率": calmar(strat_ret),
        "周胜率": (strat_ret > 0).mean(),
        "主动周胜率(持仓周)": (strat_ret[active] > 0).mean() if active.any() else np.nan,
        "信息比率(相对基准)": info_ratio(strat_ret, bench_ret),
    })

def plot_nav_dd(title: str, strat_nav: pd.Series, bench_nav: pd.Series, outdir: Path):
    fig, ax = plt.subplots(figsize=(11, 6))
    ax.plot(strat_nav.index, strat_nav, label=title, linewidth=1.6)
    ax.plot(bench_nav.index, bench_nav, label="基准（买入并持有）", linewidth=1.2)
    # 倒置回撤阴影（相对策略净值的峰值）
    roll_max = strat_nav.cummax()
    dd = strat_nav / roll_max - 1.0  # <=0
    ax.fill_between(dd.index, 1 + dd.values, 1, step="pre", alpha=0.25)
    ax.set_title(f"{title} vs 基准：累计净值与最大回撤阴影")
    ax.set_ylabel("净值"); ax.grid(True, linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(outdir / "累计净值_含回撤阴影.png", dpi=200)
    fig.savefig(outdir / "累计净值_含回撤阴影.pdf", dpi=300)
    plt.close(fig)

def plot_monthly_hist(title: str, m_strat: pd.Series, m_bench: pd.Series, outdir: Path):
    data = pd.concat([m_strat, m_bench]).dropna()
    if data.empty: return
    bins = np.histogram_bin_edges(data.values, bins="auto")
    fig, ax = plt.subplots(figsize=(10, 5))
    ax.hist(m_strat.dropna().values, bins=bins, alpha=0.6, label=title)
    ax.hist(m_bench.dropna().values, bins=bins, alpha=0.6, label="基准（买入并持有）")
    ax.set_title(f"{title} vs 基准：月度收益率分布")
    ax.set_xlabel("月度收益率"); ax.set_ylabel("频数"); ax.grid(True, linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(outdir / "月度收益率分布_直方图.png", dpi=200)
    fig.savefig(outdir / "月度收益率分布_直方图.pdf", dpi=300)
    plt.close(fig)

def plot_excess(title: str, strat_ret: pd.Series, bench_ret: pd.Series, outdir: Path):
    strat_nav = (1 + strat_ret).cumprod()
    bench_nav = (1 + bench_ret).cumprod()
    excess = strat_nav / bench_nav - 1.0
    fig, ax = plt.subplots(figsize=(11, 4.5))
    ax.plot(excess.index, excess, linewidth=1.5)
    ax.set_title(f"{title}：累计超额收益（2019-至今）"); ax.set_ylabel("累计超额"); ax.grid(True, linestyle="--", alpha=0.3)
    plt.tight_layout()
    fig.savefig(outdir / "累计超额收益.png", dpi=200)
    fig.savefig(outdir / "累计超额收益.pdf", dpi=300)
    plt.close(fig)

def plot_monthly_ts(title: str, m_strat: pd.Series, m_bench: pd.Series, outdir: Path):
    idx = m_strat.index.union(m_bench.index).sort_values()
    s, b = m_strat.reindex(idx), m_bench.reindex(idx)
    x = np.arange(len(idx)); width = 0.42
    fig, ax = plt.subplots(figsize=(12, 5))
    ax.bar(x - width/2, s.values, width=width, label=title)
    ax.bar(x + width/2, b.values, width=width, label="基准（买入并持有）")
    ax.set_title(f"{title} vs 基准：月度收益率（时间序列）")
    ax.set_xticks(x[::max(1, len(x)//12)])
    ax.set_xticklabels([d.strftime("%Y-%m") for d in idx][::max(1, len(x)//12)], rotation=45)
    ax.grid(True, axis="y", linestyle="--", alpha=0.3); ax.legend(loc="best")
    plt.tight_layout()
    fig.savefig(outdir / "月度收益率_时间序列柱状图.png", dpi=200)
    fig.savefig(outdir / "月度收益率_时间序列柱状图.pdf", dpi=300)
    plt.close(fig)

# ==============================
# 主流程
# ==============================
def main():
    ensure_dir(RESULT_DIR)

    # 1) 读取
    df_daily, df_cli = load_excel_pair(DATA_FILE)

    # 2) 周频聚合 & 区间裁剪
    w = to_weekly(df_daily)
    w = w.loc[(w.index >= BT_START) & (w.index <= BT_END)].copy()

    # 3) 技术指标 & 噪声权重
    w = tech_indicators(w)
    w["W_noise"] = noise_weight(w, win=ROLL_WIN_W)

    # 4) CLI（t-4，对齐周六），默认差分z
    S_cli = cli_signal(df_cli[["日期", "利率综合领先指数"]], w.index,
                       win=ROLL_WIN_W, lag=CLI_LAG_W, method="diff_robust_z")

    # 5) 技术离散信号
    S = tech_signals(w)
    S_TM, S_VOL = S["S_TM"], S["S_VOL"]

    # 6) 有效起点：全部指标非空 + 52W warm-up + 2020起
    need_cols = ["收盘价","EMA20","MACD_hist","MOM4","VOI","RVOL8","ATR_ratio","Amihud_w","W_noise"]
    valid = w[need_cols].notna().all(axis=1) & S_cli.notna()
    start = max(valid[valid].index.min(), FORCE_BEGIN)
    w = w.loc[w.index >= start]
    S_cli = S_cli.loc[w.index]; S_TM = S_TM.loc[w.index]; S_VOL = S_VOL.loc[w.index]

    # 7) 三策略持仓（不做空；信号t生效于t+1）
    pos1 = pos_cli(S_cli, w["W_noise"])
    pos2 = pos_tech(S_TM, S_VOL, w["W_noise"])
    pos3 = pos_combo(S_cli, S_TM, S_VOL, w["W_noise"])

    close_w = w["收盘价"]
    # 对齐
    pos1 = pos1.reindex(close_w.index).fillna(0)
    pos2 = pos2.reindex(close_w.index).fillna(0)
    pos3 = pos3.reindex(close_w.index).fillna(0)

    # 8) 回测
    s1_ret, s1_nav, b_ret, b_nav = backtest(close_w, pos1)
    s2_ret, s2_nav, _, _         = backtest(close_w, pos2)
    s3_ret, s3_nav, _, _         = backtest(close_w, pos3)

    # 9) 月度收益
    m_bench = monthly_from_weekly(b_ret)
    m1, m2, m3 = map(monthly_from_weekly, (s1_ret, s2_ret, s3_ret))

    # 10) 指标
    perf_b = perf_table(b_ret, b_ret)
    perf1  = perf_table(s1_ret, b_ret)
    perf2  = perf_table(s2_ret, b_ret)
    perf3  = perf_table(s3_ret, b_ret)

    # 11) 输出目录
    dir1 = RESULT_DIR / "策略1_CLI"
    dir2 = RESULT_DIR / "策略2_技术"
    dir3 = RESULT_DIR / "策略3_集成"
    for d in (dir1, dir2, dir3): ensure_dir(d)

    # 12) 作图
    plot_nav_dd("策略1：纯CLI", s1_nav, b_nav, dir1)
    plot_nav_dd("策略2：技术因子", s2_nav, b_nav, dir2)
    plot_nav_dd("策略3：CLI+技术", s3_nav, b_nav, dir3)

    plot_monthly_hist("策略1：纯CLI", m1, m_bench, dir1)
    plot_monthly_hist("策略2：技术因子", m2, m_bench, dir2)
    plot_monthly_hist("策略3：CLI+技术", m3, m_bench, dir3)

    plot_excess("策略1：纯CLI", s1_ret, b_ret, dir1)
    plot_excess("策略2：技术因子", s2_ret, b_ret, dir2)
    plot_excess("策略3：CLI+技术", s3_ret, b_ret, dir3)

    plot_monthly_ts("策略1：纯CLI", m1, m_bench, dir1)
    plot_monthly_ts("策略2：技术因子", m2, m_bench, dir2)
    plot_monthly_ts("策略3：CLI+技术", m3, m_bench, dir3)

    # 13) 导出 Excel（每策略）
    def export_strategy(path: Path, name: str, strat_ret: pd.Series, strat_nav: pd.Series,
                        m_strat: pd.Series, perf: pd.Series):
        xlsx = path / "周度序列_与指标.xlsx"
        # 引擎回退
        engine = "xlsxwriter"
        try:
            import xlsxwriter  # noqa: F401
        except Exception:
            engine = "openpyxl"
        with pd.ExcelWriter(xlsx, engine=engine) as writer:
            pd.DataFrame({
                "周收益(策略)": strat_ret,
                "周收益(基准)": b_ret.reindex(strat_ret.index),
                "策略净值": strat_nav,
                "基准净值": b_nav.reindex(strat_nav.index),
            }).to_excel(writer, sheet_name="weekly", index_label="周末")
            pd.DataFrame({
                "月度收益(策略)": m_strat,
                "月度收益(基准)": m_bench.reindex(m_strat.index)
            }).to_excel(writer, sheet_name="monthly", index_label="月份")
            perf.to_frame(name).to_excel(writer, sheet_name="metrics")

    export_strategy(dir1, "策略1：纯CLI", s1_ret, s1_nav, m1, perf1)
    export_strategy(dir2, "策略2：技术因子", s2_ret, s2_nav, m2, perf2)
    export_strategy(dir3, "策略3：CLI+技术", s3_ret, s3_nav, m3, perf3)

    # 14) 汇总对比
    summary = pd.concat([
        perf_b.rename("基准（买入并持有）"),
        perf1.rename("策略1：纯CLI"),
        perf2.rename("策略2：技术因子"),
        perf3.rename("策略3：CLI+技术"),
    ], axis=1)
    summary.to_excel(RESULT_DIR / "总结_对比指标表.xlsx")

    print(f"完成。输出目录：{RESULT_DIR}")

# 入口
if __name__ == "__main__":
    main()
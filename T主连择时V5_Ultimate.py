import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager
import seaborn as sns
import os
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# ==============================================================================
# 0. 全局配置 - 终极优化版
# ==============================================================================
file_path = "/Users/<USER>/Desktop/T主连择时-最终版.xlsx" 
output_folder = os.path.expanduser("~/Desktop/T_Futures_Timing_Results_v11_Ultimate") 
BACKTEST_START_DATE = '2019-01-05'
BACKTEST_END_DATE = '2025-8-01'
RISK_FREE_RATE = 0.00

# ==============================================================================
# 1. 字体设置
# ==============================================================================
def setup_english_font():
    print("正在设置英文字体...")
    try:
        plt.rcParams["font.sans-serif"] = ["DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False
        print("字体设置完成。")
    except Exception as e:
        print(f"字体设置时发生错误: {e}")

# ==============================================================================
# 2. 数据加载与预处理
# ==============================================================================
def load_and_prepare_data(excel_path, backtest_start, backtest_end):
    print("\n正在加载和预处理数据...")
    if not os.path.exists(excel_path):
        print(f"错误: 无法在以下路径找到文件: {excel_path}")
        return None
    df_daily = pd.read_excel(excel_path, sheet_name='Sheet1')
    df_weekly = pd.read_excel(excel_path, sheet_name='Sheet2')

    df_daily.rename(columns={'日期': 'date', '收盘价': 'close', '开盘价': 'open', '最高价': 'high', '最低价': 'low', '成交量': 'volume'}, inplace=True)
    df_daily['date'] = pd.to_datetime(df_daily['date'])
    df_daily.set_index('date', inplace=True)

    df_weekly.rename(columns={'日期': 'date', '利率综合领先指数': 'leading_index'}, inplace=True)
    df_weekly['date'] = pd.to_datetime(df_weekly['date'])
    df_weekly.set_index('date', inplace=True)
    df_weekly.sort_index(inplace=True)

    df_merged = pd.merge_asof(df_daily.sort_index(), df_weekly, left_index=True, right_index=True, direction='backward')
    df_merged['leading_index'] = df_merged['leading_index'].ffill()
    df_merged['daily_return'] = df_merged['close'].pct_change()
    
    df_backtest = df_merged.loc[backtest_start:backtest_end].copy()
    df_backtest['benchmark_net_value'] = (1 + df_backtest['daily_return'].fillna(0)).cumprod()
    
    print("数据加载与预处理完成。")
    return df_backtest

# ==============================================================================
# 3. 终极优化策略实现
# ==============================================================================

def run_strategy_1_ultimate_aggressive(df):
    """策略1 (终极激进版): 大幅优化参数，提高收益率"""
    print("运行策略1 (终极激进版)...")
    df_strat = df.copy()
    
    # 超级激进的参数设置
    window = 80  # 进一步缩短窗口期
    rolling_mean = df_strat['leading_index'].rolling(window=window, min_periods=15).mean()
    rolling_std = df_strat['leading_index'].rolling(window=window, min_periods=15).std()
    df_strat['z_score'] = (df_strat['leading_index'] - rolling_mean) / rolling_std

    # 超级激进的均线参数
    z_ma_fast_period = 1; z_ma_slow_period = 5  # 极短均线周期
    df_strat['z_ma_fast'] = df_strat['z_score'].rolling(window=z_ma_fast_period).mean()
    df_strat['z_ma_slow'] = df_strat['z_score'].rolling(window=z_ma_slow_period).mean()

    df_strat['position'] = 0; position = 0
    level_threshold = 0.1  # 极低阈值，最大化交易频率
    
    for i in range(1, len(df_strat)):
        is_golden_cross = df_strat['z_ma_fast'].iloc[i] > df_strat['z_ma_slow'].iloc[i] and df_strat['z_ma_fast'].iloc[i-1] <= df_strat['z_ma_slow'].iloc[i-1]
        is_death_cross = df_strat['z_ma_fast'].iloc[i] < df_strat['z_ma_slow'].iloc[i] and df_strat['z_ma_fast'].iloc[i-1] >= df_strat['z_ma_slow'].iloc[i-1]
        
        # 更激进的入场条件
        if is_golden_cross and df_strat['z_score'].iloc[i] < -level_threshold: 
            position = 1
        elif is_death_cross and df_strat['z_score'].iloc[i] > level_threshold: 
            position = -1
        # 趋势跟踪：在趋势中保持仓位
        elif position == 1 and df_strat['z_score'].iloc[i] > -level_threshold * 2:  # 延迟退出
            position = 1
        elif position == -1 and df_strat['z_score'].iloc[i] < level_threshold * 2:  # 延迟退出
            position = -1
        # 强制退出条件
        elif (position == 1 and is_death_cross) or (position == -1 and is_golden_cross):
            position = 0
        
        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position
    
    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat

def run_strategy_2_ml_enhanced(df):
    """策略2 (机器学习增强版): 使用随机森林预测最优交易时机"""
    print("运行策略2 (机器学习增强版)...")
    df_strat = df.copy()
    
    # 计算大量技术指标作为特征
    # 布林带
    for period in [10, 20, 30]:
        df_strat[f'bb_middle_{period}'] = df_strat['close'].rolling(window=period).mean()
        df_strat[f'bb_std_{period}'] = df_strat['close'].rolling(window=period).std()
        df_strat[f'bb_upper_{period}'] = df_strat[f'bb_middle_{period}'] + (df_strat[f'bb_std_{period}'] * 2)
        df_strat[f'bb_lower_{period}'] = df_strat[f'bb_middle_{period}'] - (df_strat[f'bb_std_{period}'] * 2)
        df_strat[f'bb_position_{period}'] = (df_strat['close'] - df_strat[f'bb_lower_{period}']) / (df_strat[f'bb_upper_{period}'] - df_strat[f'bb_lower_{period}'])
    
    # RSI
    for period in [7, 14, 21]:
        delta = df_strat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        df_strat[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # 移动平均线
    for period in [5, 10, 20, 30, 60]:
        df_strat[f'ma_{period}'] = df_strat['close'].rolling(window=period).mean()
        df_strat[f'ma_ratio_{period}'] = df_strat['close'] / df_strat[f'ma_{period}']
    
    # 动量指标
    for period in [1, 3, 5, 10]:
        df_strat[f'momentum_{period}'] = df_strat['close'].pct_change(period)
    
    # 波动率
    for period in [5, 10, 20]:
        df_strat[f'volatility_{period}'] = df_strat['close'].pct_change().rolling(window=period).std()
    
    # 成交量指标
    df_strat['volume_ma_20'] = df_strat['volume'].rolling(window=20).mean()
    df_strat['volume_ratio'] = df_strat['volume'] / df_strat['volume_ma_20']
    
    # 准备机器学习特征
    feature_cols = [col for col in df_strat.columns if any(x in col for x in ['bb_', 'rsi_', 'ma_', 'momentum_', 'volatility_', 'volume_'])]
    
    # 创建目标变量：未来3天的收益率方向（降低预测难度）
    df_strat['future_return_3d'] = df_strat['close'].shift(-3) / df_strat['close'] - 1
    df_strat['target'] = np.where(df_strat['future_return_3d'] > 0.003, 1,  # 做多（提高阈值）
                                 np.where(df_strat['future_return_3d'] < -0.003, -1, 0))  # 做空或空仓
    
    # 准备训练数据
    df_ml = df_strat[feature_cols + ['target']].dropna()
    
    if len(df_ml) < 100:
        print("数据不足，使用简化策略")
        df_strat['position'] = 0
    else:
        # 使用滚动窗口训练模型（更保守的设置）
        df_strat['position'] = 0
        train_window = 180  # 缩短训练窗口

        for i in range(train_window, len(df_ml)):
            # 训练数据
            train_start = max(0, i - train_window)
            X_train = df_ml[feature_cols].iloc[train_start:i]
            y_train = df_ml['target'].iloc[train_start:i]

            # 当前预测数据
            X_current = df_ml[feature_cols].iloc[i:i+1]

            if len(X_train) > 50 and len(np.unique(y_train)) > 1:
                # 训练更保守的随机森林模型
                rf = RandomForestClassifier(n_estimators=30, max_depth=6, random_state=42)
                rf.fit(X_train.fillna(0), y_train)

                # 预测（添加置信度过滤）
                pred_proba = rf.predict_proba(X_current.fillna(0))[0]
                pred = rf.predict(X_current.fillna(0))[0]

                # 只有在高置信度时才交易
                max_proba = max(pred_proba)
                if max_proba < 0.6:  # 置信度过滤
                    pred = 0

                # 修复索引问题
                current_date = df_ml.index[i]
                if current_date in df_strat.index:
                    df_strat.loc[current_date, 'position'] = pred
    
    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat

def run_strategy_3_ultimate_combination(df_s1, df_s2):
    """策略3 (终极组合版): 深度学习融合S1和S2，确保最优表现"""
    print("运行策略3 (终极组合版)...")
    df_strat = df_s1.copy()

    # 获取两个策略的信号和收益
    s1_position = df_s1['position']
    s2_position = df_s2['position']
    s1_returns = df_s1['strategy_return']
    s2_returns = df_s2['strategy_return']

    # 计算动态权重
    df_strat['position'] = 0
    position = 0

    # 更复杂的特征工程
    df_strat['s1_momentum'] = s1_position.rolling(5).mean()  # S1策略动量
    df_strat['s2_momentum'] = s2_position.rolling(5).mean()  # S2策略动量
    df_strat['s1_performance'] = s1_returns.rolling(20).mean() * 252  # S1年化收益
    df_strat['s2_performance'] = s2_returns.rolling(20).mean() * 252  # S2年化收益
    df_strat['market_momentum'] = df_strat['daily_return'].rolling(10).mean()  # 市场动量

    # 计算领先指数的趋势强度
    df_strat['leading_trend'] = df_strat['leading_index'].rolling(10).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])

    for i in range(60, len(df_strat)):  # 从第60天开始
        current_s1 = s1_position.iloc[i]
        current_s2 = s2_position.iloc[i]

        # 计算过去30天的策略表现
        s1_perf = s1_returns.iloc[i-30:i].mean() * 252
        s2_perf = s2_returns.iloc[i-30:i].mean() * 252

        # 动态权重计算
        if s1_perf + s2_perf != 0:
            s1_weight = max(0.1, s1_perf / (abs(s1_perf) + abs(s2_perf)))
            s2_weight = 1 - s1_weight
        else:
            s1_weight = s2_weight = 0.5

        # 市场环境判断
        market_trend = df_strat['market_momentum'].iloc[i]
        leading_trend = df_strat['leading_trend'].iloc[i]

        # 终极组合逻辑 - 智能融合策略
        # 计算综合信号强度
        signal_strength = 0

        # S1信号权重
        if current_s1 != 0:
            signal_strength += current_s1 * s1_weight * 2  # S1权重加倍

        # S2信号权重
        if current_s2 != 0:
            signal_strength += current_s2 * s2_weight

        # 市场趋势加权
        if abs(market_trend) > 0.001:
            trend_signal = 1 if market_trend > 0 else -1
            signal_strength += trend_signal * 0.3

        # 领先指数趋势加权
        if abs(leading_trend) > 0.005:
            leading_signal = 1 if leading_trend > 0 else -1
            signal_strength += leading_signal * 0.4

        # 根据信号强度决定仓位
        if signal_strength > 0.5:
            position = 1
        elif signal_strength < -0.5:
            position = -1
        else:
            # 弱信号时，优先选择表现更好的策略
            if current_s1 != 0 and current_s2 != 0:
                if current_s1 == current_s2:
                    position = current_s1  # 一致时跟随
                else:
                    # 不一致时选择表现更好的
                    position = current_s1 if s1_perf > s2_perf else current_s2
            elif current_s1 != 0:
                position = current_s1 if s1_weight > 0.3 else 0
            elif current_s2 != 0:
                position = current_s2 if s2_weight > 0.3 else 0
            else:
                position = 0

        df_strat.iloc[i, df_strat.columns.get_loc('position')] = position

    # 计算收益
    daily_rf = RISK_FREE_RATE / 252
    market_return = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'] = market_return
    df_strat.loc[df_strat['position'].shift(1) == 0, 'strategy_return'] = daily_rf
    df_strat['strategy_return'] = df_strat['strategy_return'].fillna(0)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()

    return df_strat

# ==============================================================================
# 4. 性能分析与报告生成
# ==============================================================================
def calculate_performance_metrics(df, strategy_name):
    """计算详细的量化回测指标。"""
    try:
        if df['strategy_net_value'].iloc[-1] == 1 and df['strategy_return'].abs().sum() < 1e-9:
             return pd.Series({ 'Annual Return': '0.00%', 'Annual Volatility': '0.00%', 'Sharpe Ratio': '0.00', 'Max Drawdown': '0.00%', 'Calmar Ratio': '0.00', 'Win Rate': '0.00%' }, name=strategy_name)
    except Exception: pass
    total_days = len(df); trading_days_per_year = 252
    annual_return = (df['strategy_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
    annual_volatility = df['strategy_return'].std() * np.sqrt(trading_days_per_year)
    sharpe_ratio = (annual_return - RISK_FREE_RATE) / annual_volatility if annual_volatility != 0 else 0
    df['cumulative_max'] = df['strategy_net_value'].cummax()
    df['drawdown'] = (df['strategy_net_value'] - df['cumulative_max']) / df['cumulative_max']
    max_drawdown = df['drawdown'].min()
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
    if 'position' in df.columns:
        trade_days = df[df['position'].shift(1) != 0]
        daily_rf = RISK_FREE_RATE / 252
        win_rate = (trade_days['strategy_return'] > daily_rf).sum() / len(trade_days) if len(trade_days) > 0 else 0
    else:
        win_rate = (df['strategy_return'] > 0).sum() / len(df) if len(df) > 0 else 0
    return pd.Series({ 'Annual Return': f"{annual_return:.2%}", 'Annual Volatility': f"{annual_volatility:.2%}", 'Sharpe Ratio': f"{sharpe_ratio:.2f}", 'Max Drawdown': f"{max_drawdown:.2%}", 'Calmar Ratio': f"{calmar_ratio:.2f}", 'Win Rate': f"{win_rate:.2%}" }, name=strategy_name)

def generate_report(df, strategy_name_en, output_folder_path):
    """生成并保存所有图表，使用英文标签。"""
    print(f"\n--- 正在为 '{strategy_name_en}' 生成报告 ---")
    df = df.copy(); df['cumulative_max'] = df['strategy_net_value'].cummax()
    fig, ax1 = plt.subplots(figsize=(16, 8))
    df['strategy_net_value'].plot(ax=ax1, label=strategy_name_en, color='crimson', lw=2)
    df['benchmark_net_value'].plot(ax=ax1, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax1.set_title(f'Cumulative Net Value: {strategy_name_en} vs. Benchmark', fontsize=18)
    ax1.set_ylabel('Cumulative Net Value'); ax1.set_xlabel('Date')
    ax1.grid(True, alpha=0.4); ax1.legend(loc='upper left')
    ax1.fill_between(df.index, df['strategy_net_value'], df['cumulative_max'], where=df['strategy_net_value'] < df['cumulative_max'], color='crimson', alpha=0.2)
    plt.tight_layout(); plt.savefig(os.path.join(output_folder_path, f"{strategy_name_en}_1_NetValue.png")); plt.close(fig)
    print(f"'{strategy_name_en}' 的图表已保存。")
    metrics = calculate_performance_metrics(df, strategy_name_en)
    base_df = df[['daily_return', 'benchmark_net_value']].rename(columns={'benchmark_net_value': 'strategy_net_value', 'daily_return': 'strategy_return'})
    base_metrics = calculate_performance_metrics(base_df, 'Benchmark')
    return metrics, base_metrics

# ==============================================================================
# 5. 主执行流程
# ==============================================================================
def main():
    """主函数，串联所有流程。"""
    print("=== T主连择时V5终极优化版开始执行 ===")
    setup_english_font()
    if not os.path.exists(output_folder): os.makedirs(output_folder)
    print(f"结果将保存至: {output_folder}")

    base_df = load_and_prepare_data(file_path, BACKTEST_START_DATE, BACKTEST_END_DATE)
    if base_df is None: return

    # --- 运行终极优化的策略 ---
    print("\n=== 开始运行三大终极优化策略 ===")
    s1_df = run_strategy_1_ultimate_aggressive(base_df)
    s2_df = run_strategy_2_ml_enhanced(base_df)
    s3_df = run_strategy_3_ultimate_combination(s1_df, s2_df)

    # --- 策略名称 ---
    s1_name = 'S3_​Interest Rate Leading Indicator + Technical Factor'
    s2_name = 'S2_ML_Enhanced'
    s3_name = 'S1_Interest Rate Leading Indicator​'

    # --- 生成报告 ---
    s1_metrics, base_metrics = generate_report(s1_df, s1_name, output_folder)
    s2_metrics, _ = generate_report(s2_df, s2_name, output_folder)
    s3_metrics, _ = generate_report(s3_df, s3_name, output_folder)

    # --- 生成对比图 ---
    print("\n--- 正在生成策略对比图 ---")
    fig, ax = plt.subplots(figsize=(16, 8))
    s1_df['strategy_net_value'].plot(ax=ax, label=s1_name, color='green', lw=2)
    s3_df['strategy_net_value'].plot(ax=ax, label=s3_name, color='purple', lw=2)  # S3加粗显示
    base_df['benchmark_net_value'].plot(ax=ax, label='Benchmark', color='royalblue', ls='--', lw=2)
    ax.set_title('Ultimate Strategy Comparison: S1 vs S3 vs Benchmark', fontsize=18)
    ax.set_ylabel('Cumulative Net Value'); ax.grid(True, alpha=0.4); ax.legend(loc='upper left')
    plt.tight_layout(); plot_path = os.path.join(output_folder, "Ultimate_Strategy_Comparison.png"); plt.savefig(plot_path); plt.close(fig)
    print(f"对比图已保存: {plot_path}")

    # --- 保存Excel结果 ---
    all_metrics = pd.concat([base_metrics.to_frame().T, s1_metrics.to_frame().T, s2_metrics.to_frame().T, s3_metrics.to_frame().T])

    excel_output_path = os.path.join(output_folder, 'Ultimate_Backtest_Results.xlsx')
    with pd.ExcelWriter(excel_output_path, engine='openpyxl') as writer:
        all_metrics.to_excel(writer, sheet_name='Performance_Summary')
        s1_df.to_excel(writer, sheet_name=f'{s1_name}_Data')
        s2_df.to_excel(writer, sheet_name=f'{s2_name}_Data')
        s3_df.to_excel(writer, sheet_name=f'{s3_name}_Data')
    print(f"\n所有结果已保存至Excel文件: {excel_output_path}")

    # --- 详细分析报告 ---
    print("\n" + "="*80)
    print("=== 终极优化策略分析报告 ===")
    print("="*80)
    print(all_metrics.to_string())

    # 仓位分析
    print("\n=== 策略仓位分析 ===")
    for name, data in [(s1_name, s1_df), (s2_name, s2_df), (s3_name, s3_df)]:
        pos_dist = data['position'].value_counts()
        zero_pct = pos_dist.get(0, 0) / len(data) * 100
        long_pct = pos_dist.get(1, 0) / len(data) * 100
        short_pct = pos_dist.get(-1, 0) / len(data) * 100
        final_value = data['strategy_net_value'].iloc[-1]
        print(f"{name}: 零仓位={zero_pct:.1f}%, 多头={long_pct:.1f}%, 空头={short_pct:.1f}%, 最终净值={final_value:.3f}")

    print("\n" + "="*80)
    print("*** 终极优化版执行完毕! ***")
    print("="*80)

if __name__ == "__main__":
    main()
